import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PerformanceOptimizer {
  factory PerformanceOptimizer() => _instance;
  const PerformanceOptimizer._internal();
  static const PerformanceOptimizer _instance =
      PerformanceOptimizer._internal();

  /// تحسين أداء التطبيق عند البدء
  static Future<void> optimizeAppStartup() async {
    try {
      // تحسين أداء الرسوم مع احترام المنطقة الآمنة
      if (!kIsWeb) {
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.manual,
          overlays: [
            SystemUiOverlay.top,
            SystemUiOverlay.bottom,
          ],
        );
      }

      // تحسين استجابة اللمس مع الحفاظ على المنطقة الآمنة
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );

      debugPrint('✅ تم تحسين أداء التطبيق مع المنطقة الآمنة');
    } catch (e) {
      debugPrint('❌ خطأ في تحسين الأداء: $e');
    }
  }

  /// تحسين استخدام الذاكرة
  static void optimizeMemoryUsage() {
    try {
      // تنظيف الذاكرة المؤقتة
      if (!kIsWeb) {
        // تحسين استخدام الذاكرة للصور
        PaintingBinding.instance.imageCache.clear();
        PaintingBinding.instance.imageCache.maximumSize = 100;
        PaintingBinding.instance.imageCache.maximumSizeBytes =
            50 << 20; // 50 MB
      }

      debugPrint('✅ تم تحسين استخدام الذاكرة');
    } catch (e) {
      debugPrint('❌ خطأ في تحسين الذاكرة: $e');
    }
  }

  /// تحسين أداء القوائم الطويلة
  static const int defaultCacheExtent = 1000;
  static const int maxItemsInMemory = 50;

  /// تحسين أداء الرسوم المتحركة
  static const Duration fastAnimationDuration = Duration(milliseconds: 200);
  static const Duration normalAnimationDuration = Duration(milliseconds: 300);
  static const Duration slowAnimationDuration = Duration(milliseconds: 500);

  /// تحسين أداء التحديثات
  static void optimizeUpdates() {
    try {
      // تقليل معدل التحديث للعمليات غير الحرجة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // تنظيف الذاكرة بعد كل إطار
        if (!kIsWeb) {
          WidgetsBinding.instance.scheduleFrame();
        }
      });

      debugPrint('✅ تم تحسين أداء التحديثات');
    } catch (e) {
      debugPrint('❌ خطأ في تحسين التحديثات: $e');
    }
  }

  /// تحسين أداء قاعدة البيانات
  static const int databaseBatchSize = 100;
  static const Duration databaseTimeout = Duration(seconds: 30);

  /// تحسين أداء الشبكة (للاستخدام المستقبلي)
  static const Duration networkTimeout = Duration(seconds: 15);
  static const int maxRetries = 3;

  /// تحسين أداء التخزين المحلي
  static const int maxCacheSize = 1000;
  static const Duration cacheExpiry = Duration(hours: 24);

  /// إعدادات تحسين الأداء العامة
  static const Map<String, dynamic> performanceSettings = {
    'enableFastScrolling': true,
    'enableImageCaching': true,
    'enableMemoryOptimization': true,
    'enableBatchOperations': true,
    'maxConcurrentOperations': 5,
  };

  /// تطبيق جميع التحسينات
  static Future<void> applyAllOptimizations() async {
    await optimizeAppStartup();
    optimizeMemoryUsage();
    optimizeUpdates();
    debugPrint('🚀 تم تطبيق جميع تحسينات الأداء');
  }

  /// مراقبة أداء التطبيق
  static void monitorPerformance() {
    if (kDebugMode) {
      WidgetsBinding.instance.addTimingsCallback((timings) {
        for (final timing in timings) {
          if (timing.totalSpan.inMilliseconds > 50) {
            // رفع الحد أكثر لتقليل الرسائل
            debugPrint('⚠️ إطار بطيء: ${timing.totalSpan.inMilliseconds}ms');
          }
        }
      });
    }
  }

  /// تحسين أداء القوائم الطويلة
  static const Map<String, dynamic> listOptimizations = {
    'itemExtent': 120.0, // ارتفاع ثابت للعناصر
    'cacheExtent': 500.0, // تقليل cache
    'addAutomaticKeepAlives': false,
    'addRepaintBoundaries': true,
    'addSemanticIndexes': false,
  };

  /// تحسين أداء الرسوم المتحركة
  static const Map<String, Duration> animationOptimizations = {
    'veryFast': Duration(milliseconds: 150),
    'fast': Duration(milliseconds: 200),
    'normal': Duration(milliseconds: 250),
    'slow': Duration(milliseconds: 300),
  };

  /// تحسين أداء البناء
  static void optimizeBuildPerformance() {
    // تقليل إعادة البناء غير الضرورية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تنظيف الذاكرة بعد كل إطار
      if (!kIsWeb) {
        PaintingBinding.instance.imageCache.clearLiveImages();
      }
    });
  }
}
