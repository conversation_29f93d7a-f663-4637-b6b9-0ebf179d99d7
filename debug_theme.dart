import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/theme_provider.dart';

void main() {
  print('🌙 اختبار الوضع الليلي - بدء التشغيل...');
  
  runApp(const ThemeDebugApp());
}

class ThemeDebugApp extends StatelessWidget {
  const ThemeDebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    print('🔧 بناء ThemeDebugApp...');
    
    return ChangeNotifierProvider(
      create: (_) {
        print('🔧 إنشاء ThemeProvider...');
        final provider = ThemeProvider();
        print('✅ تم إنشاء ThemeProvider');
        return provider;
      },
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          print('🎨 بناء MaterialApp - الوضع الليلي: ${themeProvider.isDarkMode}');
          
          return MaterialApp(
            title: 'اختبار الوضع الليلي',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            home: const ThemeTestScreen(),
          );
        },
      ),
    );
  }
}

class ThemeTestScreen extends StatelessWidget {
  const ThemeTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الوضع الليلي'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // معلومات الحالة
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Text(
                          'معلومات الوضع الليلي',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text('الحالة: ${themeProvider.isDarkMode ? "مفعل" : "غير مفعل"}'),
                        Text('تم التحميل: ${themeProvider.isLoaded ? "نعم" : "لا"}'),
                        Text('الثيم الحالي: ${Theme.of(context).brightness.name}'),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // مفتاح التبديل
                Card(
                  child: SwitchListTile(
                    title: const Text('تفعيل الوضع الليلي'),
                    subtitle: const Text('تبديل بين الوضع الفاتح والداكن'),
                    value: themeProvider.isDarkMode,
                    onChanged: (value) {
                      print('🔄 تبديل الوضع الليلي إلى: $value');
                      themeProvider.setDarkMode(value);
                    },
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // أزرار الاختبار
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          print('🌙 تفعيل الوضع الليلي');
                          themeProvider.setDarkMode(true);
                        },
                        child: const Text('تفعيل الليلي'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          print('☀️ تفعيل الوضع الفاتح');
                          themeProvider.setDarkMode(false);
                        },
                        child: const Text('تفعيل الفاتح'),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () {
                    print('🔄 تبديل الوضع');
                    themeProvider.toggleDarkMode();
                  },
                  child: const Text('تبديل الوضع'),
                ),
                
                const SizedBox(height: 16),
                
                // معاينة الألوان
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        const Text('معاينة الألوان'),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildColorBox(
                              context,
                              'Primary',
                              Theme.of(context).primaryColor,
                            ),
                            _buildColorBox(
                              context,
                              'Surface',
                              Theme.of(context).colorScheme.surface,
                            ),
                            _buildColorBox(
                              context,
                              'Background',
                              Theme.of(context).colorScheme.background,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildColorBox(BuildContext context, String label, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
