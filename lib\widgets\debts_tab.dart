// ═══════════════════════════════════════════════════════════════════════════════
// قائمة الديون للعميل المحدد (Customer Debts List)
// ═══════════════════════════════════════════════════════════════════════════════
// هذا الملف يحتوي على تبويب الديون داخل صفحة تفاصيل العميل
// وهو مختلف تماماً عن "نظرة عامة للديون" (debts_overview_screen.dart)
//
// الفرق:
// - قائمة الديون: تعرض ديون عميل واحد محدد فقط
// - نظرة عامة للديون: تعرض ديون جميع العملاء مجمعة
// ═══════════════════════════════════════════════════════════════════════════════

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../widgets/debt_card.dart';
import '../utils/number_formatter.dart';

// كلاس لتنسيق الأرقام مع فاصل الآلاف
class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  static const _separator = ',';

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // إزالة جميع الفواصل من النص الجديد
    final String newText = newValue.text.replaceAll(_separator, '');

    // التحقق من أن النص يحتوي على أرقام فقط
    if (newText.isEmpty) {
      return newValue.copyWith(text: '');
    }

    // التحقق من صحة الرقم
    if (double.tryParse(newText) == null) {
      return oldValue;
    }

    // تنسيق الرقم مع فاصل الآلاف
    final formatter = NumberFormat('#,###', 'en_US');
    final String formattedText = formatter.format(int.tryParse(newText) ?? 0);

    // حساب موضع المؤشر الجديد
    final int selectionIndex = formattedText.length;

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: selectionIndex),
    );
  }
}

class DebtsTab extends StatefulWidget {
  const DebtsTab({
    super.key,
    required this.customer,
    this.onViewTypeChanged,
  });
  final Customer customer;
  final ValueChanged<DebtCardViewType>? onViewTypeChanged;

  @override
  State<DebtsTab> createState() => _DebtsTabState();
}

// أنواع عرض البطاقات
enum DebtCardViewType {
  standard, // العرض العادي
  compact, // عرض مضغوط
  mini, // عرض مصغر (5 بطاقات في صف)
  table, // عرض جدولي
  grid, // عرض شبكي (محفوظ للتوافق)
  timeline, // عرض زمني (محفوظ للتوافق)
}

class _DebtsTabState extends State<DebtsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isMultiSelectMode = false;
  final Set<int> _selectedDebtIds = {};
  String _multiSelectMode = 'payment'; // 'payment' أو 'delete'

  // نوع العرض الحالي
  DebtCardViewType _currentViewType = DebtCardViewType.standard;

  // الفلتر الحالي
  String _currentFilter = 'all';

  // حالة طي الإحصائيات
  bool _isStatsCollapsed = false;

  // ScrollController للحفاظ على موضع التمرير
  final ScrollController _scrollController = ScrollController();

  // دوال عامة يمكن استدعاؤها من الخارج (محسنة)
  void selectAllDebts(List<Debt> activeDebts) {
    if (!mounted) return;

    final newSelectedIds = <int>{};
    for (final debt in activeDebts) {
      if (debt.id != null) {
        newSelectedIds.add(debt.id!);
      }
    }

    // تحقق من وجود تغيير فعلي
    final bool modeChanged = !_isMultiSelectMode;
    final bool selectionChanged =
        _selectedDebtIds.length != newSelectedIds.length ||
            !_selectedDebtIds.containsAll(newSelectedIds);

    if (modeChanged || selectionChanged) {
      _isMultiSelectMode = true;
      _selectedDebtIds.clear();
      _selectedDebtIds.addAll(newSelectedIds);
      setState(() {});
    }
  }

  void payAllDebts(List<Debt> activeDebts, DebtProvider debtProvider) {
    selectAllDebts(activeDebts);
    _showPaySelectedDebtsDialog(context, debtProvider, activeDebts);
  }

  void deleteAllDebts(List<Debt> activeDebts, DebtProvider debtProvider) {
    if (!mounted) return;

    bool needsUpdate = false;
    if (!_isMultiSelectMode) {
      _isMultiSelectMode = true;
      needsUpdate = true;
    }

    final newSelectedIds = <int>{};
    for (final debt in activeDebts) {
      if (debt.id != null) {
        newSelectedIds.add(debt.id!);
      }
    }

    if (_selectedDebtIds.length != newSelectedIds.length ||
        !_selectedDebtIds.containsAll(newSelectedIds)) {
      _selectedDebtIds.clear();
      _selectedDebtIds.addAll(newSelectedIds);
      needsUpdate = true;
    }

    if (needsUpdate) {
      setState(() {});
    }

    _deleteSelectedDebts(context, debtProvider);
  }

  // دالة لتحديث نوع العرض من الخارج (محسنة)
  void updateViewType(DebtCardViewType viewType) {
    if (!mounted || _currentViewType == viewType) return;

    setState(() {
      _currentViewType = viewType;
    });
    // إشعار الشاشة الأب بتغيير نوع العرض
    widget.onViewTypeChanged?.call(viewType);
  }

  @override
  void initState() {
    super.initState();
    // تحميل حالة طي الإحصائيات
    _loadStatsCollapseState();
    // تحميل نوع العرض المحفوظ
    _loadSavedViewType();

    // Ensure debts are loaded when tab is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      if (debtProvider.currentCustomerId != widget.customer.id) {
        debtProvider.loadCustomerDebts(widget.customer.id!);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // دالة محسنة للتعامل مع تبديل التحديد
  void _handleToggleSelection(int? debtId) {
    if (!mounted || debtId == null) return;

    bool needsUpdate = false;
    final bool wasEmpty = _selectedDebtIds.isEmpty;

    if (_selectedDebtIds.contains(debtId)) {
      _selectedDebtIds.remove(debtId);
      if (_selectedDebtIds.isEmpty && _isMultiSelectMode) {
        _isMultiSelectMode = false;
        needsUpdate = true;
      } else if (_selectedDebtIds.isNotEmpty) {
        needsUpdate = true;
      }
    } else {
      _selectedDebtIds.add(debtId);
      if (wasEmpty || !_isMultiSelectMode) {
        _isMultiSelectMode = true;
        needsUpdate = true;
      } else {
        needsUpdate = true;
      }
    }

    // فقط setState إذا كان هناك تغيير فعلي
    if (needsUpdate) {
      setState(() {});
    }
  }

  // دالة لإبطال التخزين المؤقت
  void _invalidateCache() {
    _cachedActiveDebts = null;
    _cachedOrganizedDebts = null;
    _lastDebtsLength = null;
    _lastCustomerId = null;
    _lastFilter = null;
  }

  // دالة محسنة لتحديث الديون
  Future<void> _refreshDebts(DebtProvider debtProvider) async {
    try {
      // إبطال التخزين المؤقت قبل التحديث
      _invalidateCache();

      // تحديث البيانات
      await debtProvider.loadCustomerDebts(widget.customer.id!);
    } catch (e) {
      debugPrint('Error refreshing debts: $e');
    }
  }

  // دالة محسنة لبدء التحديد المتعدد للدفع
  void _handleStartMultiSelectForPayment(int? debtId) {
    if (!mounted || debtId == null) return;

    final bool modeChanged =
        !_isMultiSelectMode || _multiSelectMode != 'payment';
    final bool selectionChanged =
        !_selectedDebtIds.contains(debtId) || _selectedDebtIds.length != 1;

    if (modeChanged || selectionChanged) {
      _isMultiSelectMode = true;
      _multiSelectMode = 'payment';
      _selectedDebtIds.clear();
      _selectedDebtIds.add(debtId);
      setState(() {});
    }
  }

  // دالة محسنة لبدء التحديد المتعدد للحذف
  void _handleStartMultiSelectForDelete(int? debtId) {
    if (!mounted || debtId == null) return;

    final bool modeChanged =
        !_isMultiSelectMode || _multiSelectMode != 'delete';
    final bool selectionChanged =
        !_selectedDebtIds.contains(debtId) || _selectedDebtIds.length != 1;

    if (modeChanged || selectionChanged) {
      _isMultiSelectMode = true;
      _multiSelectMode = 'delete';
      _selectedDebtIds.clear();
      _selectedDebtIds.add(debtId);
      setState(() {});
    }
  }

  // دالة محسنة للتعامل مع تحديد صفوف الجدول
  void _handleTableRowSelection(int? debtId, bool? selected) {
    if (!mounted || debtId == null) return;

    final bool wasSelected = _selectedDebtIds.contains(debtId);
    final bool wasEmpty = _selectedDebtIds.isEmpty;

    if (selected == true && !wasSelected) {
      _selectedDebtIds.add(debtId);
      if (wasEmpty) {
        _isMultiSelectMode = true;
        _multiSelectMode = 'payment';
      }
      setState(() {});
    } else if (selected == false && wasSelected) {
      _selectedDebtIds.remove(debtId);
      if (_selectedDebtIds.isEmpty && _isMultiSelectMode) {
        _isMultiSelectMode = false;
      }
      setState(() {});
    }
  }

  // متغيرات للتخزين المؤقت لتجنب إعادة الحساب
  List<Debt>? _cachedActiveDebts;
  Map<String, List<Debt>>? _cachedOrganizedDebts;
  int? _lastDebtsLength;
  int? _lastCustomerId;
  String? _lastFilter;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // تحسين: استخدام التخزين المؤقت لتجنب إعادة فلترة الديون في كل build
        final currentDebtsLength = debtProvider.debts.length;
        final currentCustomerId = widget.customer.id;

        if (_cachedActiveDebts == null ||
            _lastDebtsLength != currentDebtsLength ||
            _lastCustomerId != currentCustomerId) {
          _cachedActiveDebts = debtProvider.debts
              .where((debt) =>
                  debt.customerId == widget.customer.id &&
                  debt.status != DebtStatus.paid)
              .toList();
          _lastDebtsLength = currentDebtsLength;
          _lastCustomerId = currentCustomerId;
        }

        final activeDebts = _cachedActiveDebts!;

        // تحسين: استخدام التخزين المؤقت لتنظيم الديون
        if (_cachedOrganizedDebts == null ||
            _lastFilter != _currentFilter ||
            _lastDebtsLength != currentDebtsLength ||
            _lastCustomerId != currentCustomerId) {
          // تطبيق الفلترة على الديون
          final filteredDebts = _filterDebts(activeDebts);

          // تنظيم الديون حسب التاريخ مثل نظرة عامة للديون (إلا في حالة الجدول المباشر)
          _cachedOrganizedDebts = _currentFilter == 'table_direct'
              ? {'جدول مباشر': _sortDebtsForDirectTable(filteredDebts)}
              : _currentFilter == 'all'
                  ? _organizeDebtsByDate(filteredDebts)
                  : {_getFilterDisplayName(_currentFilter): filteredDebts};

          _lastFilter = _currentFilter;
        }

        final organizedDebts = _cachedOrganizedDebts!;
        final filteredDebts =
            organizedDebts.values.expand((debts) => debts).toList();

        // إذا كانت الديون فارغة (أصلية أو مفلترة)، اعرض الجدول الفارغ
        if (filteredDebts.isEmpty) {
          return Column(
            children: [
              // شريط الفلترة العلوي
              if (!_isMultiSelectMode) _buildTopFilterBar(),

              // الجدول الفارغ
              Expanded(
                  child: _buildMainContent(filteredDebts, organizedDebts,
                      activeDebts, debtProvider)),
            ],
          );
        }

        return Column(
          children: [
            // ═══════════════════════════════════════════════════════════
            // قائمة الديون للعميل المحدد (Customer Debts List)
            // هذا مختلف تماماً عن "نظرة عامة للديون" (Debts Overview)
            // ═══════════════════════════════════════════════════════════

            // شريط معلومات الديون المحددة في الأعلى
            if (_isMultiSelectMode && _selectedDebtIds.isNotEmpty)
              _buildTopSelectedDebtsInfo(activeDebts),

            // الإحصائيات العامة لقائمة ديون العميل المحدد (منفصلة عن نظرة عامة للديون)
            if (activeDebts.isNotEmpty && !_isMultiSelectMode)
              _buildGeneralQuickStats(activeDebts),

            // شريط الفلترة العلوي (موحد لجميع أنواع العرض)
            if (!_isMultiSelectMode) _buildUnifiedTopBar(activeDebts),

            // المحتوى الرئيسي
            Expanded(
              child: _buildDebtsView(organizedDebts),
            ),

            // شريط أدوات التحديد المتعدد في الأسفل
            if (_isMultiSelectMode)
              _buildBottomMultiSelectToolbar(activeDebts, debtProvider),
          ],
        );
      },
    );
  }

  // بناء المحتوى الرئيسي
  Widget _buildMainContent(
      List<Debt> filteredDebts,
      Map<String, List<Debt>> organizedDebts,
      List<Debt> activeDebts,
      DebtProvider debtProvider) {
    // إذا كانت الديون فارغة (أصلية أو مفلترة)، اعرض الجدول الفارغ
    if (filteredDebts.isEmpty) {
      return Column(
        children: [
          // شريط العرض (فقط عندما لا نكون في وضع التحديد المتعدد)
          if (!_isMultiSelectMode)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // عنوان القسم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ديون ${widget.customer.name}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                        ),
                        Text(
                          activeDebts.isEmpty
                              ? 'لا توجد ديون مسجلة'
                              : 'لا توجد ديون تطابق الفلتر المحدد',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // زر الفلترة
                  Container(
                    padding: const EdgeInsets.all(1),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(3),
                      border: Border.all(color: Colors.grey[300]!, width: 0.5),
                    ),
                    child: PopupMenuButton<String>(
                      padding: EdgeInsets.zero,
                      tooltip: 'فلترة الديون',
                      icon: Icon(
                        Icons.filter_list,
                        size: 14,
                        color: Colors.grey[700],
                      ),
                      onSelected: (String filter) {
                        if (!mounted || _currentFilter == filter) return;
                        setState(() {
                          _currentFilter = filter;
                        });
                      },
                      itemBuilder: (BuildContext context) => [
                        const PopupMenuItem<String>(
                          value: 'all',
                          child: Text('الكل'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'table_direct',
                          child: Text('جدول مباشر'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'overdue',
                          child: Text('متأخر'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'soon',
                          child: Text('قريباً'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'today',
                          child: Text('اليوم'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'yesterday',
                          child: Text('أمس'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'thisWeek',
                          child: Text('هذا الأسبوع'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'thisMonth',
                          child: Text('هذا الشهر'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'january',
                          child: Text('كانون الثاني'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'february',
                          child: Text('شباط'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'march',
                          child: Text('آذار'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'april',
                          child: Text('نيسان'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'may',
                          child: Text('أيار'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'june',
                          child: Text('حزيران'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'july',
                          child: Text('تموز'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'august',
                          child: Text('آب'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'september',
                          child: Text('أيلول'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'october',
                          child: Text('تشرين الأول'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'november',
                          child: Text('تشرين الثاني'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'december',
                          child: Text('كانون الأول'),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 8),

                  // زر تغيير نوع العرض
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: PopupMenuButton<DebtCardViewType>(
                      padding: EdgeInsets.zero,
                      tooltip: 'تغيير نوع العرض',
                      onSelected: (DebtCardViewType viewType) {
                        if (!mounted || _currentViewType == viewType) return;
                        setState(() {
                          _currentViewType = viewType;
                        });
                        // حفظ نوع العرض المختار
                        _saveViewType(viewType);
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: DebtCardViewType.standard,
                          child: _buildViewTypeMenuItem(
                            DebtCardViewType.standard,
                            Icons.view_agenda,
                            'عرض عادي',
                            'البطاقات العادية مع جميع التفاصيل',
                          ),
                        ),
                        PopupMenuItem(
                          value: DebtCardViewType.compact,
                          child: _buildViewTypeMenuItem(
                            DebtCardViewType.compact,
                            Icons.view_list,
                            'عرض مضغوط',
                            'بطاقات مضغوطة مع جميع التفاصيل',
                          ),
                        ),
                        PopupMenuItem(
                          value: DebtCardViewType.mini,
                          child: _buildViewTypeMenuItem(
                            DebtCardViewType.mini,
                            Icons.view_stream,
                            'عرض مصغر',
                            '5 بطاقات صغيرة في صف واحد',
                          ),
                        ),
                        PopupMenuItem(
                          value: DebtCardViewType.table,
                          child: _buildViewTypeMenuItem(
                            DebtCardViewType.table,
                            Icons.table_chart,
                            'عرض جدولي',
                            'جدول مع جميع التفاصيل والأعمدة',
                          ),
                        ),
                      ],
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getViewTypeIcon(_currentViewType),
                            color: Colors.grey[700],
                            size: 14,
                          ),
                          const SizedBox(width: 3),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey[700],
                            size: 12,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // عرض الجدول الفارغ مع رسالة
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => _refreshDebts(debtProvider),
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
                children: [
                  _buildTableCategoryView([]), // جدول فارغ
                ],
              ),
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        // شريط العرض (فقط عندما لا نكون في وضع التحديد المتعدد)
        if (!_isMultiSelectMode)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // عنوان القسم
                Expanded(
                  child: Row(
                    children: [
                      Icon(
                        Icons.receipt_long,
                        color: Colors.grey[600],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'قائمة الديون (${activeDebts.length})',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                    ],
                  ),
                ),

                // زر الفلترة
                Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(3),
                    border: Border.all(color: Colors.grey[300]!, width: 0.5),
                  ),
                  child: PopupMenuButton<String>(
                    padding: EdgeInsets.zero,
                    tooltip: 'فلترة الديون',
                    icon: Icon(
                      Icons.filter_list,
                      size: 14,
                      color: Colors.grey[700],
                    ),
                    onSelected: (String filter) {
                      if (!mounted || _currentFilter == filter) return;
                      setState(() {
                        _currentFilter = filter;
                      });
                    },
                    itemBuilder: (BuildContext context) => [
                      const PopupMenuItem<String>(
                        value: 'all',
                        child: Text('الكل'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'table_direct',
                        child: Text('جدول مباشر'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'overdue',
                        child: Text('متأخر'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'soon',
                        child: Text('قريباً'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'today',
                        child: Text('اليوم'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'yesterday',
                        child: Text('أمس'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'thisWeek',
                        child: Text('هذا الأسبوع'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'thisMonth',
                        child: Text('هذا الشهر'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'january',
                        child: Text('كانون الثاني'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'february',
                        child: Text('شباط'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'march',
                        child: Text('آذار'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'april',
                        child: Text('نيسان'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'may',
                        child: Text('أيار'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'june',
                        child: Text('حزيران'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'july',
                        child: Text('تموز'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'august',
                        child: Text('آب'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'september',
                        child: Text('أيلول'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'october',
                        child: Text('تشرين الأول'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'november',
                        child: Text('تشرين الثاني'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'december',
                        child: Text('كانون الأول'),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 8),

                // زر تغيير نوع العرض
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: PopupMenuButton<DebtCardViewType>(
                    padding: EdgeInsets.zero,
                    tooltip: 'تغيير نوع العرض',
                    onSelected: (DebtCardViewType viewType) {
                      if (!mounted || _currentViewType == viewType) return;
                      setState(() {
                        _currentViewType = viewType;
                      });
                      // حفظ نوع العرض المختار
                      _saveViewType(viewType);
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: DebtCardViewType.standard,
                        child: _buildViewTypeMenuItem(
                          DebtCardViewType.standard,
                          Icons.view_agenda,
                          'عرض عادي',
                          'البطاقات العادية مع جميع التفاصيل',
                        ),
                      ),
                      PopupMenuItem(
                        value: DebtCardViewType.compact,
                        child: _buildViewTypeMenuItem(
                          DebtCardViewType.compact,
                          Icons.view_list,
                          'عرض مضغوط',
                          'بطاقات مضغوطة مع جميع التفاصيل',
                        ),
                      ),
                      PopupMenuItem(
                        value: DebtCardViewType.mini,
                        child: _buildViewTypeMenuItem(
                          DebtCardViewType.mini,
                          Icons.view_stream,
                          'عرض مصغر',
                          '5 بطاقات صغيرة في صف واحد',
                        ),
                      ),
                      PopupMenuItem(
                        value: DebtCardViewType.table,
                        child: _buildViewTypeMenuItem(
                          DebtCardViewType.table,
                          Icons.table_chart,
                          'عرض جدولي',
                          'جدول مع جميع التفاصيل والأعمدة',
                        ),
                      ),
                    ],
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getViewTypeIcon(_currentViewType),
                          color: Colors.grey[700],
                          size: 14,
                        ),
                        const SizedBox(width: 3),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.grey[700],
                          size: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

        // شريط الأدوات (فقط في وضع التحديد المتعدد)
        if (_isMultiSelectMode)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _multiSelectMode == 'payment'
                    ? [Colors.green.shade600, Colors.green.shade700]
                    : [Colors.red.shade600, Colors.red.shade700],
              ),
              boxShadow: [
                BoxShadow(
                  color: (_multiSelectMode == 'payment'
                          ? Colors.green
                          : Colors.red)
                      .withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                // تحديد الكل
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedDebtIds.clear();
                        for (final debt in activeDebts) {
                          if (debt.id != null) {
                            _selectedDebtIds.add(debt.id!);
                          }
                        }
                      });
                    },
                    icon: const Icon(Icons.select_all, size: 16),
                    label: const Text(
                      'تحديد الكل',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: _multiSelectMode == 'payment'
                          ? Colors.green.shade700
                          : Colors.red.shade700,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 6,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      elevation: 1,
                    ),
                  ),
                ),
                const SizedBox(width: 6),

                // إلغاء الكل
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedDebtIds.clear();
                      });
                    },
                    icon: const Icon(Icons.clear_all, size: 16),
                    label: const Text(
                      'إلغاء الكل',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: _multiSelectMode == 'payment'
                          ? Colors.green.shade700
                          : Colors.red.shade700,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 6,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      elevation: 1,
                    ),
                  ),
                ),
                const SizedBox(width: 6),

                // زر العملية (تسديد أو حذف)
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _selectedDebtIds.isNotEmpty
                        ? () {
                            if (_multiSelectMode == 'payment') {
                              _showPaySelectedDebtsDialog(
                                context,
                                debtProvider,
                                activeDebts,
                              ).then((_) {
                                // تحديث الحالة بعد إتمام التسديد
                                setState(() {
                                  _selectedDebtIds.clear();
                                  _isMultiSelectMode = false;
                                });
                              });
                            } else {
                              _deleteSelectedDebts(context, debtProvider);
                            }
                          }
                        : null,
                    icon: Icon(
                      _multiSelectMode == 'payment'
                          ? Icons.payment
                          : Icons.delete,
                      size: 16,
                    ),
                    label: Text(
                      _multiSelectMode == 'payment' ? 'تسديد' : 'حذف',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _multiSelectMode == 'payment'
                          ? Colors.green.shade800
                          : Colors.red.shade800,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 6,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      elevation: 2,
                    ),
                  ),
                ),
              ],
            ),
          ),

        // Debts List
        Expanded(
          child: RefreshIndicator(
            onRefresh: () => _refreshDebts(debtProvider),
            child: _buildDebtsView(organizedDebts),
          ),
        ),

        // شريط المعلومات في الأسفل (فقط في وضع التحديد المتعدد)
        if (_isMultiSelectMode)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.grey.shade100, Colors.grey.shade200],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              border: Border(top: BorderSide(color: Colors.grey.shade300)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                children: [
                  // أيقونة المعلومات
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _multiSelectMode == 'payment'
                          ? Colors.green.shade100
                          : Colors.red.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _multiSelectMode == 'payment'
                            ? Colors.green.shade300
                            : Colors.red.shade300,
                      ),
                    ),
                    child: Icon(
                      Icons.info_outline,
                      color: _multiSelectMode == 'payment'
                          ? Colors.green.shade700
                          : Colors.red.shade700,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // معلومات التحديد
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'تم تحديد ${_selectedDebtIds.length} دين',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'المبلغ الإجمالي: ${NumberFormatter.formatCurrency(_calculateSelectedDebtsTotal(activeDebts))}',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: _multiSelectMode == 'payment'
                                ? Colors.green.shade700
                                : Colors.red.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // زر الإغلاق
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _isMultiSelectMode = false;
                        _selectedDebtIds.clear();
                      });
                    },
                    icon: const Icon(Icons.close, size: 14),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey.shade300,
                      foregroundColor: Colors.grey.shade700,
                      padding: const EdgeInsets.all(4),
                      minimumSize: const Size(24, 24),
                    ),
                    tooltip: 'إلغاء التحديد',
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  // تنظيم الديون حسب التاريخ (مثل نظرة عامة للديون)
  Map<String, List<Debt>> _organizeDebtsByDate(List<Debt> debts) {
    final Map<String, List<Debt>> organized = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisWeek = today.subtract(const Duration(days: 7));

    for (final debt in debts) {
      final debtDate = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      String category;

      // التحقق من انتهاء الموعد أولاً (حسب تاريخ الاستحقاق)
      if (dueDate.isBefore(today)) {
        category = 'منتهي الموعد';
      }
      // التحقق من الديون القريبة من الاستحقاق (خلال 3 أيام)
      else if (dueDate.difference(today).inDays <= 3 &&
          dueDate.isAfter(today.subtract(const Duration(days: 1)))) {
        category = 'قريباً';
      }
      // ثم التصنيف حسب تاريخ القيد
      else if (debtDate.isAtSameMomentAs(today)) {
        category = 'اليوم';
      } else if (debtDate.isAtSameMomentAs(yesterday)) {
        category = 'أمس';
      } else if (debtDate.isAfter(thisWeek)) {
        category = 'هذا الأسبوع';
      } else if (debtDate.year == today.year && debtDate.month == today.month) {
        category = 'هذا الشهر';
      } else {
        // تصنيف حسب الشهر
        switch (debtDate.month) {
          case 1:
            category = 'شهر الواحد';
            break;
          case 2:
            category = 'شهر الثاني';
            break;
          case 3:
            category = 'شهر الثالث';
            break;
          case 4:
            category = 'شهر الرابع';
            break;
          case 5:
            category = 'شهر الخامس';
            break;
          case 6:
            category = 'شهر السادس';
            break;
          case 7:
            category = 'شهر السابع';
            break;
          case 8:
            category = 'شهر الثامن';
            break;
          case 9:
            category = 'شهر التاسع';
            break;
          case 10:
            category = 'شهر العاشر';
            break;
          case 11:
            category = 'شهر الحادي عشر';
            break;
          case 12:
            category = 'شهر الثاني عشر';
            break;
          default:
            category = 'سابقاً';
        }
      }

      organized.putIfAbsent(category, () => []);
      organized[category]!.add(debt);
    }

    // ترتيب الفئات حسب الأولوية (اليوم أولاً ثم أمس)
    final orderedCategories = [
      'اليوم',
      'أمس',
      'منتهي الموعد',
      'قريباً',
      'هذا الأسبوع',
      'هذا الشهر',
      'شهر الواحد',
      'شهر الثاني',
      'شهر الثالث',
      'شهر الرابع',
      'شهر الخامس',
      'شهر السادس',
      'شهر السابع',
      'شهر الثامن',
      'شهر التاسع',
      'شهر العاشر',
      'شهر الحادي عشر',
      'شهر الثاني عشر',
      'سابقاً',
    ];
    final Map<String, List<Debt>> orderedResult = {};

    for (final category in orderedCategories) {
      if (organized.containsKey(category)) {
        // ترتيب الديون داخل كل فئة حسب التاريخ (الأحدث أولاً)
        organized[category]!.sort((a, b) => b.entryDate.compareTo(a.entryDate));
        orderedResult[category] = organized[category]!;
      }
    }

    return orderedResult;
  }

  // ترتيب الديون للجدول المباشر حسب الأولوية
  List<Debt> _sortDebtsForDirectTable(List<Debt> debts) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // نسخ القائمة وترتيبها
    final sortedDebts = List<Debt>.from(debts);

    sortedDebts.sort((a, b) {
      final aEntryDate =
          DateTime(a.entryDate.year, a.entryDate.month, a.entryDate.day);
      final bEntryDate =
          DateTime(b.entryDate.year, b.entryDate.month, b.entryDate.day);
      final aDaysUntilDue =
          DateTime(a.dueDate.year, a.dueDate.month, a.dueDate.day)
              .difference(today)
              .inDays;
      final bDaysUntilDue =
          DateTime(b.dueDate.year, b.dueDate.month, b.dueDate.day)
              .difference(today)
              .inDays;

      // حساب الأولوية لكل دين
      final aPriority =
          _getDebtPriority(aEntryDate, aDaysUntilDue, today, yesterday);
      final bPriority =
          _getDebtPriority(bEntryDate, bDaysUntilDue, today, yesterday);

      // ترتيب حسب الأولوية أولاً
      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }

      // إذا كانت الأولوية متساوية، ترتيب حسب تاريخ القيد (الأحدث أولاً)
      return b.entryDate.compareTo(a.entryDate);
    });

    return sortedDebts;
  }

  // حساب أولوية الدين للترتيب
  int _getDebtPriority(DateTime entryDate, int daysUntilDue, DateTime today,
      DateTime yesterday) {
    // الأولوية: المتأخرة (0) > اليوم (1) > أمس (2) > قريباً (3) > هذا الأسبوع (4) > أخرى (5)

    // متأخرة (أولوية عالية)
    if (daysUntilDue < 0) {
      return 0;
    }

    // اليوم
    if (entryDate.isAtSameMomentAs(today)) {
      return 1;
    }

    // أمس
    if (entryDate.isAtSameMomentAs(yesterday)) {
      return 2;
    }

    // قريباً (خلال 3 أيام)
    if (daysUntilDue >= 0 && daysUntilDue <= 3) {
      return 3;
    }

    // هذا الأسبوع
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    if (entryDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
        entryDate.isBefore(today)) {
      return 4;
    }

    // أخرى
    return 5;
  }

  // Calculate total amount of selected debts
  double _calculateSelectedDebtsTotal(List<Debt> activeDebts) {
    double total = 0.0;
    for (final debt in activeDebts) {
      if (_selectedDebtIds.contains(debt.id)) {
        total += debt.remainingAmount;
      }
    }
    return total;
  }

  // Delete selected debts
  Future<void> _deleteSelectedDebts(
    BuildContext context,
    DebtProvider debtProvider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        elevation: 30,
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          constraints: const BoxConstraints(maxWidth: 400, minHeight: 300),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: Colors.red.shade200, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.3),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: 5,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with animated icon
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.red.withValues(alpha: 0.1),
                        Colors.red.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.delete_forever_rounded,
                    color: Colors.red.shade700,
                    size: 50,
                  ),
                ),
                const SizedBox(height: 24),

                // Title with emphasis
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '⚠️ تأكيد حذف الديون',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade800,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 20),

                // Content with enhanced styling
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.red.withValues(alpha: 0.08),
                        Colors.red.withValues(alpha: 0.03),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'هل أنت متأكد من حذف ${_selectedDebtIds.length} دين محدد؟',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.black87,
                          height: 1.6,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '🚨 لا يمكن التراجع عن هذا الإجراء',
                          style: TextStyle(
                            fontSize: 15,
                            color: Colors.red.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 28),

                // Enhanced action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => Navigator.pop(context, false),
                        icon: const Icon(Icons.close, size: 18),
                        label: const Text('إلغاء'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side:
                              BorderSide(color: Colors.grey.shade500, width: 2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          backgroundColor: Colors.grey.shade50,
                        ).copyWith(
                          foregroundColor:
                              WidgetStateProperty.all(Colors.grey.shade700),
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => Navigator.pop(context, true),
                        icon: const Icon(Icons.delete_forever, size: 20),
                        label: const Text('حذف نهائياً'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 4,
                          shadowColor: Colors.red.withValues(alpha: 0.5),
                        ).copyWith(
                          textStyle: WidgetStateProperty.all(
                            const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );

    if (confirmed == true) {
      try {
        // حذف الديون واحد تلو الآخر
        for (final debtId in _selectedDebtIds) {
          await debtProvider.deleteDebt(debtId);
        }

        // تنظيف حالة التحديد وإبطال التخزين المؤقت
        _invalidateCache();
        setState(() {
          _selectedDebtIds.clear();
          _isMultiSelectMode = false;
        });

        // تحديث إضافي للتأكد من تحديث العرض المصغر
        if (mounted) {
          setState(() {});
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الديون المحددة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الديون: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Show payment dialog for selected debts
  Future<void> _showPaySelectedDebtsDialog(
    BuildContext context,
    DebtProvider debtProvider,
    List<Debt> activeDebts,
  ) async {
    final selectedDebts = activeDebts
        .where((debt) => _selectedDebtIds.contains(debt.id))
        .toList();

    final totalAmount = _calculateSelectedDebtsTotal(activeDebts);
    bool isFullPayment = true;
    final amountController = TextEditingController(
      text: NumberFormat('#,###', 'en_US').format(totalAmount.toInt()),
    );
    final notesController = TextEditingController();
    DateTime selectedDate = DateTime.now();

    // متغير لتحديد الدين الذي سيستكمل المبلغ منه
    Debt? selectedDebtForRemainder =
        selectedDebts.isNotEmpty ? selectedDebts.first : null;

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 40,
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.95,
            height: MediaQuery.of(context).size.height * 0.85,
            constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.green.shade50],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.green, Colors.green.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.payment,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                selectedDebts.length == 1
                                    ? 'تسديد الدين'
                                    : 'تسديد الديون المحددة',
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                selectedDebts.length == 1
                                    ? 'دين واحد'
                                    : '${selectedDebts.length} دين محدد',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context, false),
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.grey.shade100,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Payment Type Selector
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.shade50,
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = true;
                                  amountController.text = NumberFormat(
                                    '#,###',
                                    'en_US',
                                  ).format(totalAmount.toInt());
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    bottomLeft: Radius.circular(12),
                                  ),
                                  gradient: isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.green,
                                            Colors.green.shade600,
                                          ],
                                        )
                                      : null,
                                  color:
                                      isFullPayment ? null : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payment,
                                      color: isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد كامل',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = false;
                                  amountController.clear();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(12),
                                    bottomRight: Radius.circular(12),
                                  ),
                                  gradient: !isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.orange,
                                            Colors.orange.shade600,
                                          ],
                                        )
                                      : null,
                                  color: !isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payments,
                                      color: !isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد جزئي',
                                      style: TextStyle(
                                        color: !isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Summary Card
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'المبلغ الإجمالي:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                NumberFormatter.formatCurrency(totalAmount),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Amount Field - قابل للتعديل دائماً
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.shade300,
                          width: 2,
                        ),
                      ),
                      child: TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          ThousandsSeparatorInputFormatter(),
                        ],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          labelText: 'مبلغ التسديد (قابل للتعديل)',
                          labelStyle: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                          suffixText: 'د.ع',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                          prefixIcon: Icon(
                            Icons.edit,
                            color: Colors.blue.shade600,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                        onChanged: (value) {
                          // إزالة الفواصل للحصول على الرقم الفعلي
                          final cleanValue = value.replaceAll(',', '');
                          final amount = double.tryParse(cleanValue);
                          if (amount != null) {
                            setState(() {
                              isFullPayment = amount >= totalAmount;
                            });
                          }
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // حقل تاريخ التسديد
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.green.shade300,
                          width: 2,
                        ),
                      ),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.calendar_today,
                            color: Colors.green.shade700,
                            size: 20,
                          ),
                        ),
                        title: const Text(
                          'تاريخ التسديد',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        subtitle: Text(
                          DateFormat('yyyy/MM/dd - EEEE', 'ar')
                              .format(selectedDate),
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        trailing: Icon(
                          Icons.edit_calendar,
                          color: Colors.green.shade600,
                        ),
                        onTap: () async {
                          final DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: selectedDate,
                            firstDate: DateTime(2020),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                            locale: const Locale('ar'),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: ColorScheme.light(
                                    primary: Colors.green.shade600,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (picked != null && picked != selectedDate) {
                            setState(() {
                              selectedDate = picked;
                            });
                          }
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // قائمة الديون المحددة - تظهر فقط عند وجود أكثر من دين واحد
                    if (selectedDebts.length > 1)
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Colors.blue.shade50, Colors.blue.shade100],
                          ),
                          border: Border.all(
                            color: Colors.blue.shade200,
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Header احترافي
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.blue.shade600,
                                    Colors.blue.shade700,
                                  ],
                                ),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.account_balance_wallet,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        const Text(
                                          'الديون المحددة للتسديد',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          '${selectedDebts.length} دين • ${NumberFormatter.formatCurrency(totalAmount)}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.white.withValues(
                                              alpha: 0.9,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      '${selectedDebts.length}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // قائمة الديون المحسنة
                            Container(
                              constraints: const BoxConstraints(maxHeight: 200),
                              child: ListView.separated(
                                shrinkWrap: true,
                                padding: const EdgeInsets.all(12),
                                itemCount: selectedDebts.length,
                                separatorBuilder: (context, index) =>
                                    const SizedBox(height: 8),
                                itemBuilder: (context, index) {
                                  final debt = selectedDebts[index];
                                  final isSelected =
                                      selectedDebtForRemainder?.id == debt.id;

                                  return Container(
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? Colors.white
                                          : Colors.white.withValues(alpha: 0.7),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: isSelected
                                            ? Colors.blue.shade400
                                            : Colors.blue.shade200,
                                        width: isSelected ? 2 : 1,
                                      ),
                                      boxShadow: isSelected
                                          ? [
                                              BoxShadow(
                                                color: Colors.blue.withValues(
                                                  alpha: 0.2,
                                                ),
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ]
                                          : null,
                                    ),
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          selectedDebtForRemainder = debt;
                                        });
                                      },
                                      borderRadius: BorderRadius.circular(12),
                                      child: Padding(
                                        padding: const EdgeInsets.all(12),
                                        child: Row(
                                          children: [
                                            // Radio Button مخصص
                                            Container(
                                              width: 20,
                                              height: 20,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: isSelected
                                                      ? Colors.blue.shade600
                                                      : Colors.grey.shade400,
                                                  width: 2,
                                                ),
                                                color: isSelected
                                                    ? Colors.blue.shade600
                                                    : Colors.transparent,
                                              ),
                                              child: isSelected
                                                  ? const Icon(
                                                      Icons.check,
                                                      color: Colors.white,
                                                      size: 12,
                                                    )
                                                  : null,
                                            ),
                                            const SizedBox(width: 12),

                                            // معلومات الدين
                                            Expanded(
                                              child: Column(
                                                children: [
                                                  // نوع البطاقة الأصلي واسم العميل
                                                  Column(
                                                    children: [
                                                      // نوع البطاقة الأصلي
                                                      Row(
                                                        children: [
                                                          Icon(
                                                            Icons.credit_card,
                                                            size: 14,
                                                            color: isSelected
                                                                ? Colors.blue
                                                                    .shade700
                                                                : Colors.orange
                                                                    .shade600,
                                                          ),
                                                          const SizedBox(
                                                            width: 4,
                                                          ),
                                                          Text(
                                                            _convertCardTypeToArabic(
                                                              debt.cardType,
                                                            ),
                                                            style: TextStyle(
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: isSelected
                                                                  ? Colors.blue
                                                                      .shade800
                                                                  : Colors
                                                                      .orange
                                                                      .shade700,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 2),
                                                      // اسم العميل
                                                      Row(
                                                        children: [
                                                          Icon(
                                                            Icons.person,
                                                            size: 12,
                                                            color: isSelected
                                                                ? Colors.blue
                                                                    .shade600
                                                                : Colors.grey
                                                                    .shade600,
                                                          ),
                                                          const SizedBox(
                                                            width: 4,
                                                          ),
                                                          Text(
                                                            widget
                                                                .customer.name,
                                                            style: TextStyle(
                                                              fontSize: 12,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              color: isSelected
                                                                  ? Colors.blue
                                                                      .shade700
                                                                  : Colors.grey
                                                                      .shade700,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 6),

                                                  // المبلغ والكمية
                                                  Row(
                                                    children: [
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 8,
                                                          vertical: 2,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: isSelected
                                                              ? Colors.green
                                                                  .shade100
                                                              : Colors.grey
                                                                  .shade100,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                            6,
                                                          ),
                                                        ),
                                                        child: Text(
                                                          NumberFormatter
                                                              .formatCurrency(
                                                            debt.remainingAmount,
                                                          ),
                                                          style: TextStyle(
                                                            fontSize: 11,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: isSelected
                                                                ? Colors.green
                                                                    .shade700
                                                                : Colors.grey
                                                                    .shade700,
                                                          ),
                                                        ),
                                                      ),
                                                      if (debt.quantity >
                                                          1) ...[
                                                        const SizedBox(
                                                            width: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                            horizontal: 6,
                                                            vertical: 2,
                                                          ),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: isSelected
                                                                ? Colors.orange
                                                                    .shade100
                                                                : Colors.grey
                                                                    .shade100,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                              6,
                                                            ),
                                                          ),
                                                          child: Text(
                                                            'الكمية ${debt.quantity}',
                                                            style: TextStyle(
                                                              fontSize: 10,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              color: isSelected
                                                                  ? Colors
                                                                      .orange
                                                                      .shade700
                                                                  : Colors.grey
                                                                      .shade600,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ],
                                                  ),
                                                  const SizedBox(height: 4),

                                                  // التواريخ
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.calendar_today,
                                                        size: 10,
                                                        color: Colors
                                                            .grey.shade500,
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        _formatDate(
                                                          debt.entryDate,
                                                        ),
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          color: Colors
                                                              .grey.shade600,
                                                        ),
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Icon(
                                                        Icons.schedule,
                                                        size: 10,
                                                        color: Colors
                                                            .orange.shade400,
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        _formatDate(
                                                            debt.dueDate),
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          color: Colors
                                                              .orange.shade600,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),

                                            // مؤشر التحديد
                                            if (isSelected)
                                              Container(
                                                padding:
                                                    const EdgeInsets.all(4),
                                                decoration: BoxDecoration(
                                                  color: Colors.blue.shade600,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons.check,
                                                  color: Colors.white,
                                                  size: 12,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),

                            // معلومات الدين المحدد للاستكمال
                            if (selectedDebtForRemainder != null)
                              Container(
                                margin:
                                    const EdgeInsets.fromLTRB(12, 0, 12, 12),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.green.shade50,
                                      Colors.green.shade100,
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.green.shade300,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade600,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.trending_up,
                                        color: Colors.white,
                                        size: 14,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Text(
                                            'دين الاستكمال المحدد',
                                            style: TextStyle(
                                              fontSize: 11,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.green.shade800,
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.credit_card,
                                                size: 14,
                                                color: Colors.green.shade700,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                _convertCardTypeToArabic(
                                                  selectedDebtForRemainder!
                                                      .cardType,
                                                ),
                                                style: TextStyle(
                                                  fontSize: 13,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.green.shade800,
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              Icon(
                                                Icons.person,
                                                size: 12,
                                                color: Colors.green.shade600,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                widget.customer.name,
                                                style: TextStyle(
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w500,
                                                  color: Colors.green.shade700,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Text(
                                            'قيد: ${_formatDate(selectedDebtForRemainder!.entryDate)} • استحقاق: ${_formatDate(selectedDebtForRemainder!.dueDate)}',
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.green.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 12),

                    // Notes Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: TextField(
                        controller: notesController,
                        maxLines: 2,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          labelStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                          hintText: 'أضف أي ملاحظات حول التسديد...',
                          hintStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 15,
                          ),
                          prefixIcon: Icon(
                            Icons.note_alt_outlined,
                            color: Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context, false),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(color: Colors.grey.shade400),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () => Navigator.pop(context, true),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'تأكيد التسديد',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    if (confirmed == true) {
      // إزالة الفواصل من النص للحصول على الرقم الفعلي
      final cleanText = amountController.text.replaceAll(',', '');
      final amount = double.tryParse(cleanText);
      if (amount == null || amount <= 0) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى إدخال مبلغ صحيح'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      try {
        // منطق التسديد المحسن
        if (isFullPayment || amount >= totalAmount) {
          // تسديد كامل - سدد كل الديون بالكامل
          for (final debt in selectedDebts) {
            await debtProvider.makePayment(
              debt.id!,
              debt.remainingAmount,
              PaymentType.full,
              notesController.text.trim().isEmpty
                  ? null
                  : notesController.text.trim(),
              paymentDate: selectedDate,
            );
          }
        } else {
          // تسديد جزئي - وزع المبلغ على الديون
          double remainingAmount = amount;

          // سدد الديون الأخرى أولاً (ما عدا الدين المحدد لاستكمال المبلغ)
          for (final debt in selectedDebts) {
            if (debt.id == selectedDebtForRemainder?.id) continue;

            final paymentAmount = math.min(
              remainingAmount,
              debt.remainingAmount,
            );
            if (paymentAmount > 0) {
              await debtProvider.makePayment(
                debt.id!,
                paymentAmount,
                paymentAmount >= debt.remainingAmount
                    ? PaymentType.full
                    : PaymentType.partial,
                notesController.text.trim().isEmpty
                    ? null
                    : notesController.text.trim(),
                paymentDate: selectedDate,
              );
              remainingAmount -= paymentAmount;
            }
          }

          // سدد المبلغ المتبقي من الدين المحدد
          if (remainingAmount > 0 && selectedDebtForRemainder != null) {
            final paymentAmount = math.min(
              remainingAmount,
              selectedDebtForRemainder!.remainingAmount,
            );
            if (paymentAmount > 0) {
              await debtProvider.makePayment(
                selectedDebtForRemainder!.id!,
                paymentAmount,
                paymentAmount >= selectedDebtForRemainder!.remainingAmount
                    ? PaymentType.full
                    : PaymentType.partial,
                notesController.text.trim().isEmpty
                    ? null
                    : notesController.text.trim(),
                paymentDate: selectedDate,
              );
            }
          }
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(selectedDebts.length == 1
                  ? 'تم تسديد الدين بنجاح'
                  : 'تم تسديد الديون المحددة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في التسديد: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // دالة لتنسيق التاريخ
  String _formatDate(DateTime date) {
    final formatter = DateFormat('yyyy/MM/dd', 'en');
    return formatter.format(date);
  }

  // دالة لتحويل أسماء البطاقات إلى العربية
  String _convertCardTypeToArabic(String cardType) {
    final cleanType = cardType.trim().toLowerCase();

    // التعامل مع البطاقات المخصصة باستخدام CardTypeProvider
    if (cleanType.startsWith('custom_')) {
      try {
        final cardTypeProvider = Provider.of<CardTypeProvider>(
          context,
          listen: false,
        );

        // استخراج الرقم من custom_X
        final cardTypeId = int.parse(cleanType.replaceFirst('custom_', ''));

        // البحث عن البطاقة المخصصة
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );

        return customCardType.displayName;
      } catch (e) {
        // في حالة عدم العثور على البطاقة، استخدم الأسماء الافتراضية
        final customNumber = cleanType.replaceAll('custom_', '');
        switch (customNumber) {
          case '1':
          case '3':
            return 'أبو الستة'; // فئة 6000
          case '2':
          case '4':
            return 'آسيا'; // فئة 5000
          case '5':
          case '6':
            return 'زين'; // فئة 5000
          case '7':
          case '8':
            return 'أبو العشرة'; // فئة 10000
          default:
            return 'بطاقة مخصصة $customNumber';
        }
      }
    }

    // التعامل مع الأنواع الافتراضية
    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia':
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      default:
        // إذا كان النص عربي، أعده كما هو
        if (_isArabicText(cardType)) {
          return cardType.isNotEmpty ? cardType : 'غير محدد';
        }
        // إذا كان إنجليزي ولم نجده، أعده كما هو
        return cardType.isNotEmpty ? cardType : 'غير محدد';
    }
  }

  // دالة للتحقق من النص العربي
  bool _isArabicText(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  // دالة لبناء عرض الديون حسب النوع المحدد مع التصنيف
  Widget _buildDebtsView(Map<String, List<Debt>> organizedDebts) {
    if (organizedDebts.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد ديون',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView(
      key: PageStorageKey('debts_list_${widget.customer.id}'),
      controller: _scrollController,
      padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
      children: organizedDebts.entries.map((entry) {
        final dateCategory = entry.key;
        final categoryDebts = entry.value;

        return _buildDateCategorySection(dateCategory, categoryDebts);
      }).toList(),
    );
  }

  // بناء قسم فئة التاريخ
  Widget _buildDateCategorySection(String dateCategory, List<Debt> debts) {
    // إذا كان جدول مباشر، عرض الجدول مباشرة بدون عنوان فئة
    if (dateCategory == 'جدول مباشر') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض الجدول مباشرة
          _buildTableCategoryView(debts),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان الفئة الزمنية
        _buildCategoryHeader(dateCategory, debts),
        const SizedBox(height: 8),

        // بطاقة تصنيف أنواع الكروت (مخفية في العرض الجدولي)
        if (_currentViewType != DebtCardViewType.table) ...[
          _buildCardTypesClassificationCard(debts),
          const SizedBox(height: 8),
        ],

        // بطاقات الديون حسب نوع العرض
        _buildCategoryDebtsView(debts),
        const SizedBox(height: 16),
      ],
    );
  }

  // بناء بطاقة تصنيف أنواع الكروت
  Widget _buildCardTypesClassificationCard(List<Debt> debts) {
    // تجميع الديون حسب نوع الكارت
    final cardTypeGroups = <String, Map<String, dynamic>>{};

    for (final debt in debts) {
      final cardTypeName = _getCardTypeDisplayName(debt.cardType);
      if (!cardTypeGroups.containsKey(cardTypeName)) {
        cardTypeGroups[cardTypeName] = {
          'count': 0,
          'totalQuantity': 0,
          'totalAmount': 0.0,
          'remainingAmount': 0.0,
        };
      }

      cardTypeGroups[cardTypeName]!['count'] += 1;
      cardTypeGroups[cardTypeName]!['totalQuantity'] += debt.quantity;
      cardTypeGroups[cardTypeName]!['totalAmount'] += debt.amount;
      cardTypeGroups[cardTypeName]!['remainingAmount'] += debt.remainingAmount;
    }

    // إذا لم توجد أنواع كروت، لا تعرض البطاقة
    if (cardTypeGroups.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.indigo.shade50, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.indigo.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان البطاقة
          Row(
            children: [
              Icon(
                Icons.category,
                color: Colors.indigo.shade600,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'تصنيف أنواع الكروت',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade700,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.indigo.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${cardTypeGroups.length} نوع',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // عرض أنواع الكروت في صفوف
          Wrap(
            spacing: 8,
            runSpacing: 6,
            children: cardTypeGroups.entries.map((entry) {
              final cardTypeName = entry.key;
              final data = entry.value;
              final count = data['count'] as int;
              final totalQuantity = data['totalQuantity'] as int;

              final cardTypeColor = _getCardTypeColor(cardTypeName);

              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: cardTypeColor.shade200, width: 0.5),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.credit_card,
                      size: 12,
                      color: cardTypeColor.shade600,
                    ),
                    const SizedBox(width: 6),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          cardTypeName,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: cardTypeColor.shade800,
                          ),
                        ),
                        Text(
                          '$count دين • $totalQuantity كارت',
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // بناء عنوان الفئة
  Widget _buildCategoryHeader(String category, List<Debt> debts) {
    Color categoryColor;
    IconData categoryIcon;

    switch (category) {
      case 'اليوم':
        categoryColor = Colors.green.shade600; // أخضر لبطاقات اليوم
        categoryIcon = Icons.today;
        break;
      case 'أمس':
        categoryColor =
            const Color(0xFF1A237E); // أزرق داكن مائل للأسود لبطاقات الأمس
        categoryIcon = Icons.schedule;
        break;
      case 'هذا الأسبوع':
        categoryColor = Colors.blue.shade600; // أزرق للأسبوع
        categoryIcon = Icons.date_range;
        break;
      case 'هذا الشهر':
        categoryColor = Colors.purple.shade600; // بنفسجي للشهر
        categoryIcon = Icons.calendar_month;
        break;
      case 'قريباً':
        categoryColor = Colors.orange.shade600; // برتقالي للقريب
        categoryIcon = Icons.schedule_outlined;
        break;
      case 'منتهي الموعد':
        categoryColor = Colors.red.shade600; // أحمر للمتأخر
        categoryIcon = Icons.warning;
        break;
      case 'شهر الواحد':
        categoryColor = Colors.indigo.shade600; // نيلي للشهر الأول
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الثاني':
        categoryColor = Colors.teal.shade600; // تيل للشهر الثاني
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الثالث':
        categoryColor = Colors.cyan.shade600; // سماوي للشهر الثالث
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الرابع':
        categoryColor = Colors.amber.shade700; // عنبري للشهر الرابع
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الخامس':
        categoryColor = Colors.lime.shade700; // ليموني للشهر الخامس
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر السادس':
        categoryColor = Colors.pink.shade600; // وردي للشهر السادس
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر السابع':
        categoryColor = Colors.deepOrange.shade600; // برتقالي غامق للشهر السابع
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الثامن':
        categoryColor = Colors.brown.shade600; // بني للشهر الثامن
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر التاسع':
        categoryColor = Colors.blueGrey.shade600; // رمادي مزرق للشهر التاسع
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر العاشر':
        categoryColor = Colors.deepPurple.shade600; // بنفسجي غامق للشهر العاشر
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الحادي عشر':
        categoryColor = Colors.green.shade800; // أخضر غامق للشهر الحادي عشر
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'شهر الثاني عشر':
        categoryColor = Colors.blue.shade800; // أزرق غامق للشهر الثاني عشر
        categoryIcon = Icons.calendar_view_month;
        break;
      case 'سابقاً':
        categoryColor = Colors.grey.shade600; // رمادي للسابق
        categoryIcon = Icons.history;
        break;
      default:
        categoryColor = Colors.red.shade600; // أحمر افتراضي للمتأخر
        categoryIcon = Icons.warning;
    }

    // حساب الكمية والمبلغ الإجمالي
    final totalQuantity =
        debts.fold<int>(0, (sum, debt) => sum + debt.remainingQuantity);
    final totalAmount =
        debts.fold<double>(0.0, (sum, debt) => sum + debt.remainingAmount);

    return Container(
      decoration: BoxDecoration(
        color: categoryColor, // استخدام لون الفئة الزمنية
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: categoryColor.withValues(alpha: 0.8)),
        boxShadow: [
          BoxShadow(
            color: categoryColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: categoryColor.withValues(alpha: 0.1),
            blurRadius: 3,
            offset: const Offset(-1, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // الجزء الأيسر مع المساحة الداخلية
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  // أيقونة الفئة مع تحسين التصميم
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3)),
                    ),
                    child: Icon(categoryIcon, color: Colors.white, size: 16),
                  ),
                  const SizedBox(width: 12),

                  // اسم الفئة
                  Expanded(
                    child: Text(
                      category,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white, // نص أبيض على الخلفية الملونة
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // بطاقة العدد
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الكمية',
                  style: TextStyle(
                    color: Colors.grey.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 3),
                Text(
                  '$totalQuantity',
                  style: TextStyle(
                    color: Colors.grey.shade800,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 8),

          // بطاقة المبلغ على اليمين
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // الأيقونة الخضراء
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green.shade600,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
                const SizedBox(width: 8),
                // المبلغ والعنوان
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _formatAmountWithThousandsSeparator(totalAmount),
                          style: TextStyle(
                            color: Colors.grey.shade800,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'ألف',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 9,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء عرض ديون الفئة حسب نوع العرض
  Widget _buildCategoryDebtsView(List<Debt> debts) {
    switch (_currentViewType) {
      case DebtCardViewType.standard:
        return _buildStandardCategoryView(debts);
      case DebtCardViewType.compact:
        return _buildCompactCategoryView(debts);
      case DebtCardViewType.mini:
        return _buildMiniCategoryView(debts);
      case DebtCardViewType.table:
        return _buildTableCategoryView(debts);
      case DebtCardViewType.grid:
        return _buildStandardCategoryView(debts);
      case DebtCardViewType.timeline:
        return _buildStandardCategoryView(debts);
    }
  }

  // العرض العادي للفئة
  Widget _buildStandardCategoryView(List<Debt> debts) {
    return Column(
      children: debts.map((debt) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: GestureDetector(
            onLongPress: _isMultiSelectMode
                ? null
                : () {
                    _showMultiSelectOptionsModal(debt);
                  },
            child: DebtCard(
              debt: debt,
              isSelected: _selectedDebtIds.contains(debt.id),
              isMultiSelectMode: _isMultiSelectMode,
              isMiniView: _currentViewType == DebtCardViewType.mini,
              isStandardView: _currentViewType == DebtCardViewType.standard,
              onToggleSelection: () => _handleToggleSelection(debt.id),
              onStartMultiSelectForPayment: () =>
                  _handleStartMultiSelectForPayment(debt.id),
              onStartMultiSelectForDelete: () =>
                  _handleStartMultiSelectForDelete(debt.id),
              onDelete: () {
                // إبطال التخزين المؤقت فقط - التحديث سيحدث تلقائياً من Provider
                _invalidateCache();
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  // العرض المضغوط للفئة
  Widget _buildCompactCategoryView(List<Debt> debts) {
    return Column(
      children: debts.map((debt) {
        // التحقق من انتهاء الموعد وتاريخ أمس واليوم
        final isOverdue = debt.dueDate.isBefore(DateTime.now());
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final yesterday = today.subtract(const Duration(days: 1));
        final debtDate = DateTime(
          debt.entryDate.year,
          debt.entryDate.month,
          debt.entryDate.day,
        );
        final isToday = debtDate.isAtSameMomentAs(today);
        final isYesterday = debtDate.isAtSameMomentAs(yesterday);

        // تحديد لون البطاقة - الأولوية لانتهاء الموعد
        Color cardColor;
        Color shadowColor;

        if (isOverdue) {
          // منتهي الموعد - أحمر دائماً
          cardColor = Colors.red.shade600;
          shadowColor = Colors.red.withValues(alpha: 0.3);
        } else if (isYesterday) {
          // أمس وليس منتهي الموعد - أزرق داكن
          cardColor = const Color(0xFF1A237E);
          shadowColor = const Color(0xFF1A237E).withValues(alpha: 0.4);
        } else if (isToday) {
          // اليوم - أبيض مع ظل أخضر
          cardColor = Colors.white;
          shadowColor = Colors.green.withValues(alpha: 0.1);
        } else {
          // باقي الحالات - أبيض عادي
          cardColor = Colors.white;
          shadowColor = Colors.grey.withValues(alpha: 0.05);
        }

        final isColored = isOverdue || isYesterday;
        final isSelected = _selectedDebtIds.contains(debt.id);

        return GestureDetector(
          onTap: _isMultiSelectMode
              ? () {
                  setState(() {
                    if (_selectedDebtIds.contains(debt.id)) {
                      _selectedDebtIds.remove(debt.id);
                      if (_selectedDebtIds.isEmpty) {
                        _isMultiSelectMode = false;
                      }
                    } else if (debt.id != null) {
                      _selectedDebtIds.add(debt.id!);
                    }
                  });
                }
              : null,
          onLongPress: _isMultiSelectMode
              ? null
              : () {
                  _showMultiSelectOptionsModal(debt);
                },
          child: Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? (_multiSelectMode == 'payment'
                        ? Colors.green
                        : Colors.red)
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: shadowColor,
                  blurRadius: isColored ? 2 : 1,
                  offset: const Offset(0, 0.5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف الأول: اسم العميل والمبلغ
                Row(
                  children: [
                    // أيقونة التحديد
                    if (_isMultiSelectMode) ...[
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (_multiSelectMode == 'payment'
                                  ? Colors.green
                                  : Colors.red)
                              : Colors.grey.shade300,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isSelected ? Icons.check : Icons.circle_outlined,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    // اسم العميل
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.person,
                            color:
                                isColored ? Colors.white : Colors.blue.shade600,
                            size: 14,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'العميل:',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: isColored
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              widget.customer.name,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color:
                                    isColored ? Colors.white : Colors.black87,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 12),

                    // المبلغ
                    Row(
                      children: [
                        Icon(
                          Icons.attach_money,
                          color:
                              isColored ? Colors.white : Colors.green.shade600,
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'المبلغ:',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isColored
                                ? Colors.white.withValues(alpha: 0.8)
                                : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${(debt.amount / 1000).toStringAsFixed(3)} ألف',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: isColored
                                ? Colors.white
                                : Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 6),

                // الصف الثاني: اسم الكارت والكمية
                Row(
                  children: [
                    // اسم الكارت
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.credit_card,
                            color: isColored
                                ? Colors.white
                                : Colors.orange.shade600,
                            size: 10,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'اسم الكارت:',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: isColored
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _convertCardTypeToArabic(debt.cardType),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: isColored
                                    ? Colors.white
                                    : Colors.orange.shade700,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 12),

                    // الكمية
                    Row(
                      children: [
                        Icon(
                          Icons.inventory,
                          color:
                              isColored ? Colors.white : Colors.purple.shade600,
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'الكمية:',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isColored
                                ? Colors.white.withValues(alpha: 0.8)
                                : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${debt.quantity}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: isColored
                                ? Colors.white
                                : Colors.purple.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 6),

                // الصف الثالث: تاريخ القيد والوقت
                Row(
                  children: [
                    // تاريخ القيد
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            color:
                                isColored ? Colors.white : Colors.blue.shade600,
                            size: 14,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'تاريخ القيد:',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: isColored
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _formatDateWithDayName(debt.entryDate,
                                  includeTime: false),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: isColored
                                    ? Colors.white
                                    : Colors.blue.shade700,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 12),

                    // الوقت
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          color:
                              isColored ? Colors.white : Colors.orange.shade600,
                          size: 12,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatTimeWithAmPm(debt.entryDate),
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.w500,
                            color: isColored
                                ? Colors.white.withValues(alpha: 0.9)
                                : Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 6),

                // الصف الرابع: تاريخ الاستحقاق
                Row(
                  children: [
                    Icon(
                      Icons.event,
                      color: isColored ? Colors.white : Colors.red.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'تاريخ الاستحقاق:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _formatDateWithDayName(debt.dueDate,
                            includeTime: false),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: isColored ? Colors.white : Colors.red.shade700,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 6),

                // الصف الخامس: العدادات
                Row(
                  children: [
                    // عداد منذ القيد
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: isColored
                              ? Colors.white.withValues(alpha: 0.2)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isColored
                                ? Colors.white.withValues(alpha: 0.4)
                                : Colors.grey.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isColored
                                  ? Colors.black.withValues(alpha: 0.1)
                                  : Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // عنوان العداد
                            Row(
                              children: [
                                Icon(
                                  Icons.schedule,
                                  color: isColored
                                      ? Colors.white
                                      : Colors.grey.shade600,
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'منذ القيد',
                                  style: TextStyle(
                                    fontSize: 9,
                                    fontWeight: FontWeight.w600,
                                    color: isColored
                                        ? Colors.white.withValues(alpha: 0.8)
                                        : Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            // قيمة العداد
                            Text(
                              _getTimeSinceEntry(debt.entryDate),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: isColored
                                    ? Colors.white
                                    : Colors.grey.shade800,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // عداد المتبقي للموعد
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: isColored
                              ? Colors.white.withValues(alpha: 0.2)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isColored
                                ? Colors.white.withValues(alpha: 0.4)
                                : Colors.grey.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isColored
                                  ? Colors.black.withValues(alpha: 0.1)
                                  : Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // عنوان العداد
                            Row(
                              children: [
                                Icon(
                                  _getTimeUntilDue(debt.dueDate)
                                          .contains('متأخر')
                                      ? Icons.warning
                                      : Icons.timer,
                                  color: isColored
                                      ? Colors.white
                                      : (_getTimeUntilDue(debt.dueDate)
                                              .contains('متأخر')
                                          ? Colors.red.shade600
                                          : Colors.grey.shade600),
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'للموعد',
                                  style: TextStyle(
                                    fontSize: 9,
                                    fontWeight: FontWeight.w600,
                                    color: isColored
                                        ? Colors.white.withValues(alpha: 0.8)
                                        : Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            // قيمة العداد
                            Text(
                              _getTimeUntilDue(debt.dueDate),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: isColored
                                    ? Colors.white
                                    : (_getTimeUntilDue(debt.dueDate)
                                            .contains('متأخر')
                                        ? Colors.red.shade700
                                        : Colors.grey.shade800),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                // التفاصيل (إذا وجدت)
                if (debt.notes?.isNotEmpty == true) ...[
                  const SizedBox(height: 6),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.notes,
                        color:
                            isColored ? Colors.white : Colors.indigo.shade600,
                        size: 14,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'التفاصيل:',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: isColored
                              ? Colors.white.withValues(alpha: 0.8)
                              : Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          debt.notes!,
                          style: TextStyle(
                            fontSize: 11,
                            color: isColored
                                ? Colors.white.withValues(alpha: 0.9)
                                : Colors.indigo.shade700,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  // العرض المصغر للفئة - مثل بطاقات التسديد مع صفين
  Widget _buildMiniCategoryView(List<Debt> debts) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 0.7, // تقليل النسبة لجعل البطاقة أطول
      ),
      itemCount: debts.length,
      itemBuilder: (context, index) {
        final debt = debts[index];
        return GestureDetector(
          onTap:
              _isMultiSelectMode ? () => _handleToggleSelection(debt.id) : null,
          onLongPress: !_isMultiSelectMode
              ? () => _showMultiSelectOptionsModal(debt)
              : null,
          child: Container(
            margin: const EdgeInsets.all(1),
            decoration: BoxDecoration(
              gradient: _selectedDebtIds.contains(debt.id)
                  ? LinearGradient(
                      colors: [Colors.blue.shade100, Colors.white],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [Colors.grey.shade50, Colors.white],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: _selectedDebtIds.contains(debt.id)
                    ? Colors.blue.shade400
                    : Colors.grey.withValues(alpha: 0.2),
                width: _selectedDebtIds.contains(debt.id) ? 2 : 1,
              ),
              boxShadow: [
                if (_selectedDebtIds.contains(debt.id))
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  )
                else
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
              ],
            ),
            child: Column(
              children: [
                // شريط تمييز حالة الدين في الأعلى
                _buildDebtStatusStrip(debt),

                // محتوى البطاقة
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 6,
                      right: 6,
                      top: 6,
                      bottom: 2,
                    ),
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // عدادات الأيام في الهيدر
                              Row(
                                children: [
                                  // عداد منذ القيد
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.teal.shade50,
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                          color: Colors.teal.shade300,
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.access_time,
                                                size: 10,
                                                color: Colors.teal.shade600,
                                              ),
                                              const SizedBox(width: 2),
                                              Text(
                                                'منذ القيد',
                                                style: TextStyle(
                                                  fontSize: 8,
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.teal.shade700,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            '${_calculateDaysSinceEntry(debt.entryDate)} يوم',
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.teal.shade800,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  const SizedBox(width: 6),

                                  // عداد الأيام المتبقية
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 4),
                                      decoration: BoxDecoration(
                                        color:
                                            _getRemainingDaysColor(debt.dueDate)
                                                .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                          color: _getRemainingDaysColor(
                                                  debt.dueDate)
                                              .withValues(alpha: 0.3),
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.schedule,
                                                size: 10,
                                                color: _getRemainingDaysColor(
                                                    debt.dueDate),
                                              ),
                                              const SizedBox(width: 2),
                                              Text(
                                                'متبقي',
                                                style: TextStyle(
                                                  fontSize: 8,
                                                  fontWeight: FontWeight.w600,
                                                  color: _getRemainingDaysColor(
                                                      debt.dueDate),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            _formatRemainingDays(debt.dueDate),
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                              color: _getRemainingDaysColor(
                                                  debt.dueDate),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 4),

                              // Header مع نوع الكارت والكمية
                              Row(
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.inventory_2,
                                          size: 14,
                                          color: Colors.grey[600],
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'الكمية: ${NumberFormatter.formatNumber(debt.quantity)}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Color(0xFF2C3E50),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // نوع الكارت
                                  Consumer<CardTypeProvider>(
                                    builder: (context, cardTypeProvider, _) {
                                      final cardTypeOption = cardTypeProvider
                                          .getCardTypeById(debt.cardType);
                                      final displayName =
                                          cardTypeOption?.displayName ??
                                              'غير محدد';

                                      return Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 6, vertical: 3),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                            color: Colors.blue
                                                .withValues(alpha: 0.3),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.credit_card,
                                              size: 10,
                                              color: Colors.blue.shade600,
                                            ),
                                            const SizedBox(width: 3),
                                            Text(
                                              displayName,
                                              style: const TextStyle(
                                                fontSize: 8,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.black87,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),

                              const SizedBox(height: 4),

                              // معلومات المبلغ
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[50],
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text(
                                          'المبلغ الإجمالي:',
                                          style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.black87),
                                        ),
                                        Text(
                                          NumberFormatter.formatCurrency(
                                              debt.amount),
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black87,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 1),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text(
                                          'المبلغ المتبقي:',
                                          style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.black87),
                                        ),
                                        Text(
                                          NumberFormatter.formatCurrency(
                                              debt.remainingAmount),
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 3),

                              // بطاقة حالة الدين
                              Center(
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: debt.remainingAmount > 0
                                        ? Colors.red.withValues(alpha: 0.1)
                                        : Colors.green.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: debt.remainingAmount > 0
                                          ? Colors.red.withValues(alpha: 0.3)
                                          : Colors.green.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Text(
                                    debt.remainingAmount > 0
                                        ? 'غير مسدد'
                                        : 'مسدد',
                                    style: TextStyle(
                                      color: debt.remainingAmount > 0
                                          ? Colors.red
                                          : Colors.green,
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(height: 3),

                              // التواريخ والأوقات - وقت العملية وتاريخ القيد وتاريخ الاستحقاق
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: Colors.teal.withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    // وقت العملية
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.access_time,
                                          size: 10,
                                          color: Colors.purple.shade600,
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                'وقت العملية',
                                                style: TextStyle(
                                                  fontSize: 8,
                                                  color: Colors.black87,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              Text(
                                                _formatTime(debt.entryDate),
                                                style: const TextStyle(
                                                  fontSize: 8,
                                                  color: Colors.black87,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 2),
                                    // تاريخ القيد
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.calendar_today,
                                          size: 10,
                                          color: Colors.teal.shade600,
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                'تاريخ القيد',
                                                style: TextStyle(
                                                  fontSize: 8,
                                                  color: Colors.black87,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              Text(
                                                _formatDateArabic(
                                                    debt.entryDate),
                                                style: const TextStyle(
                                                  fontSize: 8,
                                                  color: Colors.black87,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 2),
                                    // تاريخ الاستحقاق
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.schedule,
                                          size: 10,
                                          color: Colors.orange.shade600,
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                'تاريخ الاستحقاق',
                                                style: TextStyle(
                                                  fontSize: 8,
                                                  color: Colors.black87,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              Text(
                                                _formatDateArabic(debt.dueDate),
                                                style: const TextStyle(
                                                  fontSize: 8,
                                                  color: Colors.black87,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 3),

                              // الملاحظات (إذا كانت موجودة)
                              if (debt.notes != null &&
                                  debt.notes!.isNotEmpty) ...[
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: Colors.blue.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.note,
                                        size: 10,
                                        color: Colors.blue.shade600,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'ملاحظات:',
                                              style: TextStyle(
                                                fontSize: 8,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.black87,
                                              ),
                                            ),
                                            Text(
                                              debt.notes!,
                                              style: const TextStyle(
                                                fontSize: 8,
                                                color: Colors.black87,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 3),
                              ],

                              const SizedBox(height: 6),

                              // أزرار العمليات المصغرة
                              Row(
                                children: [
                                  // زر التسديد
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () => _payDebt(debt),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green.shade600,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4, vertical: 2),
                                        minimumSize: const Size(0, 20),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      child: const Text(
                                        'تسديد',
                                        style: TextStyle(
                                          fontSize: 8,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),

                                  const SizedBox(width: 2),

                                  // زر التعديل
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () =>
                                          _showDebtActionBottomSheet(debt),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue.shade600,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4, vertical: 2),
                                        minimumSize: const Size(0, 20),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      child: const Text(
                                        'تعديل',
                                        style: TextStyle(
                                          fontSize: 8,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),

                                  const SizedBox(width: 2),

                                  // زر الحذف
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () => _deleteDebt(debt),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red.shade600,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4, vertical: 2),
                                        minimumSize: const Size(0, 20),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      child: const Text(
                                        'حذف',
                                        style: TextStyle(
                                          fontSize: 8,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // أيقونة التحديد
                        if (_isMultiSelectMode)
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: _selectedDebtIds.contains(debt.id)
                                    ? Colors.blue
                                    : Colors.grey.shade300,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                              child: _selectedDebtIds.contains(debt.id)
                                  ? const Icon(
                                      Icons.done,
                                      color: Colors.white,
                                      size: 10,
                                    )
                                  : null,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // دالة للحصول على أيقونة نوع العرض
  IconData _getViewTypeIcon(DebtCardViewType viewType) {
    switch (viewType) {
      case DebtCardViewType.standard:
        return Icons.view_agenda;
      case DebtCardViewType.compact:
        return Icons.view_list;
      case DebtCardViewType.mini:
        return Icons.view_stream;
      case DebtCardViewType.table:
        return Icons.table_chart;
      case DebtCardViewType.grid:
        return Icons.grid_view;
      case DebtCardViewType.timeline:
        return Icons.timeline;
    }
  }

  // عرض نافذة خيارات التحديد المتعدد
  void _showMultiSelectOptionsModal(Debt debt) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض النافذة
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'خيارات متعددة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ),

            const SizedBox(height: 20),

            // خيار التسديد المتعدد
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.payment,
                  color: Colors.green.shade600,
                  size: 20,
                ),
              ),
              title: const Text('تسديد متعدد'),
              subtitle: const Text('حدد عدة ديون لتسديدها دفعة واحدة'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _isMultiSelectMode = true;
                  _multiSelectMode = 'payment';
                  _selectedDebtIds.clear();
                  if (debt.id != null) {
                    _selectedDebtIds.add(debt.id!);
                  }
                });
              },
            ),

            // خيار الحذف المتعدد
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.delete,
                  color: Colors.red.shade600,
                  size: 20,
                ),
              ),
              title: const Text('حذف متعدد'),
              subtitle: const Text('حدد عدة ديون لحذفها دفعة واحدة'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _isMultiSelectMode = true;
                  _multiSelectMode = 'delete';
                  _selectedDebtIds.clear();
                  if (debt.id != null) {
                    _selectedDebtIds.add(debt.id!);
                  }
                });
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // عرض نافذة خيارات الدين (للضغط المطول على صف الجدول)
  void _showDebtActionBottomSheet(Debt debt) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض النافذة
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'خيارات الدين',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_getCardTypeDisplayName(debt.cardType)} - ${debt.quantity} قطعة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 10),

            // خيار التسديد
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.payment,
                  color: Colors.green.shade600,
                  size: 20,
                ),
              ),
              title: const Text('تسديد الدين'),
              subtitle:
                  Text('تسديد ${NumberFormatter.formatCurrency(debt.amount)}'),
              onTap: () {
                Navigator.pop(context);
                _payDebt(debt);
              },
            ),

            // خيار الحذف
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.delete,
                  color: Colors.red.shade600,
                  size: 20,
                ),
              ),
              title: const Text('حذف الدين'),
              subtitle: const Text('حذف هذا الدين نهائياً'),
              onTap: () {
                Navigator.pop(context);
                _deleteDebt(debt);
              },
            ),

            // خيار التحديد المتعدد
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.checklist,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
              ),
              title: const Text('تحديد متعدد'),
              subtitle: const Text('تحديد عدة ديون للتسديد أو الحذف'),
              onTap: () {
                Navigator.pop(context);
                _showMultiSelectOptionsModal(debt);
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // تسديد دين واحد
  void _payDebt(Debt debt) {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    setState(() {
      _isMultiSelectMode = true;
      _multiSelectMode = 'payment';
      _selectedDebtIds.clear();
      if (debt.id != null) {
        _selectedDebtIds.add(debt.id!);
      }
    });

    // استخدام دالة التسديد المتعدد الموجودة
    final activeDebts = debtProvider.debts
        .where((d) =>
            d.customerId == widget.customer.id && d.status != DebtStatus.paid)
        .toList();
    _showPaySelectedDebtsDialog(context, debtProvider, activeDebts);
  }

  // حذف دين واحد
  void _deleteDebt(Debt debt) async {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    setState(() {
      _isMultiSelectMode = true;
      _selectedDebtIds.clear();
      if (debt.id != null) {
        _selectedDebtIds.add(debt.id!);
      }
    });

    // استخدام دالة الحذف المتعدد الموجودة
    await _deleteSelectedDebts(context, debtProvider);
  }

  // دالة لبناء عنصر قائمة نوع العرض
  Widget _buildViewTypeMenuItem(
    DebtCardViewType viewType,
    IconData icon,
    String title,
    String description,
  ) {
    final isSelected = _currentViewType == viewType;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: Colors.blue.withValues(alpha: 0.3))
            : null,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.blue.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.blue.shade700 : Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.blue.shade800 : Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            Icon(
              Icons.check_circle,
              color: Colors.blue.shade600,
              size: 20,
            ),
        ],
      ),
    );
  }

  // دالة لحفظ نوع العرض المختار
  Future<void> _saveViewType(DebtCardViewType viewType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('debt_view_type', viewType.name);
      // إشعار الشاشة الأب بتغيير نوع العرض
      widget.onViewTypeChanged?.call(viewType);
    } catch (e) {
      debugPrint('خطأ في حفظ نوع العرض: $e');
    }
  }

  // دالة لتحميل نوع العرض المحفوظ
  Future<void> _loadSavedViewType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedViewType = prefs.getString('debt_view_type');

      if (savedViewType != null) {
        final viewType = DebtCardViewType.values.firstWhere(
          (e) => e.name == savedViewType,
          orElse: () => DebtCardViewType.standard,
        );

        setState(() {
          _currentViewType = viewType;
        });
        // إشعار الشاشة الأب بنوع العرض المحمل
        widget.onViewTypeChanged?.call(viewType);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نوع العرض: $e');
    }
  }

  // دالة لحفظ حالة طي الإحصائيات
  Future<void> _saveStatsCollapseState(bool isCollapsed) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('stats_collapsed', isCollapsed);
    } catch (e) {
      debugPrint('خطأ في حفظ حالة طي الإحصائيات: $e');
    }
  }

  // دالة لتحميل حالة طي الإحصائيات
  Future<void> _loadStatsCollapseState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isCollapsed = prefs.getBool('stats_collapsed') ?? false;
      setState(() {
        _isStatsCollapsed = isCollapsed;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل حالة طي الإحصائيات: $e');
    }
  }

  // دالة لتنسيق المبلغ مع فاصل الآلاف
  String _formatAmountWithThousandsSeparator(double amount) {
    // تحويل إلى آلاف
    final amountInThousands = amount / 1000;

    // تنسيق مع 3 خانات عشرية وفاصل الآلاف
    final formatter = NumberFormat('#,##0.000', 'en_US');
    return formatter.format(amountInThousands);
  }

  // دالة لتنسيق التاريخ مع اسم اليوم
  String _formatDateWithDayName(DateTime date, {bool includeTime = true}) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    String dayName;
    if (dateOnly.isAtSameMomentAs(today)) {
      dayName = 'اليوم';
    } else if (dateOnly.isAtSameMomentAs(yesterday)) {
      dayName = 'أمس';
    } else {
      // أسماء الأيام بالعربية
      const arabicDays = [
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت',
        'الأحد'
      ];
      dayName = arabicDays[date.weekday - 1];
    }

    final dateFormatter = DateFormat('yyyy/MM/dd');
    final formattedDate = dateFormatter.format(date);

    if (includeTime) {
      final timeFormatter = DateFormat('HH:mm');
      final formattedTime = timeFormatter.format(date);
      return '$dayName $formattedDate $formattedTime';
    } else {
      return '$dayName $formattedDate';
    }
  }

  // دالة لتنسيق الوقت مع AM/PM
  String _formatTimeWithAmPm(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;

    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'ص';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'ص';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'م';
    } else {
      displayHour = hour - 12;
      period = 'م';
    }

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  // دالة لحساب الوقت منذ القيد
  String _getTimeSinceEntry(DateTime entryDate) {
    final now = DateTime.now();
    final difference = now.difference(entryDate);

    if (difference.inDays > 0) {
      final days = difference.inDays;
      final hours = difference.inHours % 24;
      if (hours > 0) {
        return '$days يوم $hours ساعة';
      } else {
        return '$days يوم';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // دالة لحساب الوقت المتبقي للاستحقاق
  String _getTimeUntilDue(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now);

    if (difference.isNegative) {
      final overdueDays = now.difference(dueDate).inDays;
      if (overdueDays > 0) {
        return 'متأخر $overdueDays يوم';
      } else {
        return 'متأخر اليوم';
      }
    } else if (difference.inDays > 0) {
      return 'متبقي للموعد ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'متبقي للموعد ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'متبقي للموعد ${difference.inMinutes} دقيقة';
    } else {
      return 'ينتهي الآن';
    }
  }

  // العرض الجدولي للفئة (بدون Consumer إضافي)
  Widget _buildTableCategoryView(List<Debt> debts) {
    // استخدام Provider.of بدلاً من Consumer لتجنب التحديث الغير ضروري
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    return _buildTableCategoryViewContent(debts, debtProvider);
  }

  // محتوى العرض الجدولي للفئة
  Widget _buildTableCategoryViewContent(
      List<Debt> debts, DebtProvider debtProvider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // رأس الجدول مع العدادات المصغرة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, Colors.teal.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                // العنوان
                Row(
                  children: [
                    Icon(
                      Icons.table_chart,
                      color: Colors.teal.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.customer.name,
                      style: TextStyle(
                        color: Colors.teal.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // العدادات المصغرة قابلة للتمرير
                SizedBox(
                  height: 85, // زيادة الارتفاع لاستيعاب التخطيط العمودي
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 4), // إضافة حشو أفقي
                    child: Row(
                      children: [
                        // عدادات أنواع الكروت منفصلة
                        ...(() {
                          // تجميع الديون حسب نوع الكارت مع حساب الكمية المتبقية
                          final cardTypeGroups =
                              <String, Map<String, double>>{};
                          for (final debt in debts) {
                            if (!cardTypeGroups.containsKey(debt.cardType)) {
                              cardTypeGroups[debt.cardType] = {
                                'quantity': 0,
                                'amount': 0,
                                'remainingAmount': 0
                              };
                            }

                            // حساب الكمية المتبقية بناءً على المبلغ المتبقي
                            final cardValue = debt.amount /
                                debt.quantity; // قيمة الكارت الواحد
                            final remainingQuantity = debt.remainingAmount /
                                cardValue; // الكمية المتبقية

                            cardTypeGroups[debt.cardType]!['quantity'] =
                                cardTypeGroups[debt.cardType]!['quantity']! +
                                    remainingQuantity;
                            cardTypeGroups[debt.cardType]!['amount'] =
                                cardTypeGroups[debt.cardType]!['amount']! +
                                    debt.amount;
                            cardTypeGroups[debt.cardType]!['remainingAmount'] =
                                cardTypeGroups[debt.cardType]![
                                        'remainingAmount']! +
                                    debt.remainingAmount;
                          }

                          // ترتيب الأنواع حسب المبلغ (الأكثر أولاً)
                          final sortedEntries = cardTypeGroups.entries.toList()
                            ..sort((a, b) => b.value['amount']!
                                .compareTo(a.value['amount']!));

                          return sortedEntries.take(3).map((entry) {
                            final cardTypeName =
                                _getCardTypeDisplayName(entry.key);
                            return Row(
                              children: [
                                Container(
                                  width:
                                      145, // تحديد عرض ثابت أعرض (من 130 إلى 145)
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8), // تقليل الحشو لتوفير مساحة
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(
                                        12), // زيادة الانحناء
                                    border: Border.all(
                                        color: Colors.grey
                                            .shade300), // حواف رمادية خفيفة
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withValues(
                                            alpha: 0.08), // تخفيف الظل
                                        blurRadius: 4, // زيادة الضبابية
                                        offset:
                                            const Offset(0, 2), // زيادة الإزاحة
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // الأيقونة والعنوان في الوسط
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.credit_card,
                                            size: 14,
                                            color: Colors.blue.shade600,
                                          ),
                                          const SizedBox(width: 6),
                                          Text(
                                            cardTypeName,
                                            style: TextStyle(
                                              fontSize: 11,
                                              color: Colors.grey.shade700,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),

                                      // المبلغ في الوسط
                                      Text(
                                        NumberFormat('#,###', 'en_US').format(
                                            entry.value['remainingAmount']!
                                                .toInt()),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 2),

                                      // العدد في الوسط
                                      Text(
                                        '${entry.value['quantity']!.toInt()} كارت',
                                        style: TextStyle(
                                          fontSize: 9,
                                          color: Colors.grey.shade600,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 12), // زيادة المسافة
                              ],
                            );
                          }).toList();
                        })(),
                        // عداد الديون المتأخرة (يظهر فقط عند وجود ديون متأخرة)
                        ...(() {
                          final overdueDebts = debts.where((debt) {
                            final now = DateTime.now();
                            final today =
                                DateTime(now.year, now.month, now.day);
                            final dueDate = DateTime(debt.dueDate.year,
                                debt.dueDate.month, debt.dueDate.day);
                            return dueDate.isBefore(today) &&
                                debt.remainingAmount > 0;
                          }).toList();

                          if (overdueDebts.isEmpty) {
                            return <Widget>[];
                          }

                          return [
                            const SizedBox(
                                width: 12), // زيادة المسافة بين العدادات
                            Container(
                              width:
                                  145, // تحديد عرض ثابت أعرض (من 130 إلى 145)
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8), // تقليل الحشو لتوفير مساحة
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.circular(12), // زيادة الانحناء
                                border: Border.all(
                                    color: Colors
                                        .grey.shade300), // حواف رمادية خفيفة
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey
                                        .withValues(alpha: 0.08), // تخفيف الظل
                                    blurRadius: 4, // زيادة الضبابية
                                    offset: const Offset(0, 2), // زيادة الإزاحة
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // الأيقونة والعنوان في الوسط
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.warning,
                                        size: 14,
                                        color: Colors.red.shade600,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'متأخرة',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.grey.shade700,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),

                                  // المبلغ في الوسط
                                  Text(
                                    NumberFormat('#,###', 'en_US').format(debts
                                        .where((debt) {
                                          final now = DateTime.now();
                                          final today = DateTime(
                                              now.year, now.month, now.day);
                                          final dueDate = DateTime(
                                              debt.dueDate.year,
                                              debt.dueDate.month,
                                              debt.dueDate.day);
                                          return dueDate.isBefore(today) &&
                                              debt.remainingAmount > 0;
                                        })
                                        .fold<double>(
                                            0,
                                            (sum, debt) =>
                                                sum + debt.remainingAmount)
                                        .toInt()),
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 2),

                                  // العدد في الوسط
                                  Text(
                                    '${debts.where((debt) {
                                          final now = DateTime.now();
                                          final today = DateTime(
                                              now.year, now.month, now.day);
                                          final dueDate = DateTime(
                                              debt.dueDate.year,
                                              debt.dueDate.month,
                                              debt.dueDate.day);
                                          return dueDate.isBefore(today) &&
                                              debt.remainingAmount > 0;
                                        }).fold<double>(0, (sum, debt) => sum + debt.quantity).toInt()} كرت',
                                    style: TextStyle(
                                      fontSize: 9,
                                      color: Colors.grey.shade600,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12), // زيادة المسافة
                            // عداد العمليات الإجمالية
                            Container(
                              width:
                                  145, // تحديد عرض ثابت أعرض (من 130 إلى 145)
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8), // تقليل الحشو لتوفير مساحة
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.circular(12), // زيادة الانحناء
                                border: Border.all(
                                    color: Colors.grey.shade200,
                                    width: 0.5), // تخفيف الحدود
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey
                                        .withValues(alpha: 0.08), // تخفيف الظل
                                    blurRadius: 4, // زيادة الضبابية
                                    offset: const Offset(0, 2), // زيادة الإزاحة
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // الأيقونة والعنوان في الوسط
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.receipt_long,
                                        size: 14,
                                        color: Colors.purple.shade600,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'العمليات',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.grey.shade700,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),

                                  // المبلغ الإجمالي في الوسط
                                  Text(
                                    NumberFormat('#,###', 'en_US').format(debts
                                        .fold<double>(
                                            0.0,
                                            (sum, debt) =>
                                                sum + debt.remainingAmount)
                                        .toInt()),
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 2),

                                  // عدد العمليات في الوسط
                                  Text(
                                    '${debts.length} عملية',
                                    style: TextStyle(
                                      fontSize: 9,
                                      color: Colors.grey.shade600,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ];
                        })(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // محتوى الجدول
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 4.5,
              child: DataTable(
                columnSpacing: 2,
                horizontalMargin: 4,
                checkboxHorizontalMargin: 4,
                headingRowHeight: 28,
                dataRowMinHeight: _currentFilter == 'table_direct' ? 40 : 32,
                dataRowMaxHeight: _currentFilter == 'table_direct' ? 40 : 32,
                headingRowColor: WidgetStateProperty.all(Colors.teal.shade600),
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                ),
                dividerThickness: 1,
                columns: _buildTableColumns(debts),
                rows: _buildTableRows(debts),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط أدوات التحديد المتعدد في أسفل الشاشة
  Widget _buildBottomMultiSelectToolbar(
      List<Debt> debts, DebtProvider debtProvider) {
    return SafeArea(
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.only(
            left: 12, right: 12, top: 8, bottom: 16), // زيادة المسافة من الأسفل
        padding: const EdgeInsets.symmetric(
            horizontal: 12, vertical: 12), // زيادة الحشو العمودي
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withValues(alpha: 0.9),
              Colors.grey.shade100.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.grey.shade300,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // أيقونة التحديد المتعدد
            Icon(
              Icons.checklist,
              color: Colors.grey.shade700,
              size: 16,
            ),
            const SizedBox(width: 6),

            const Spacer(),

            // أزرار الإجراءات
            Row(
              children: [
                // زر التسديد (في الجهة اليمنى)
                if (_selectedDebtIds.isNotEmpty) ...[
                  ElevatedButton.icon(
                    onPressed: () {
                      _showPaySelectedDebtsDialog(
                        context,
                        debtProvider,
                        debts,
                      ).then((_) {
                        setState(() {
                          _selectedDebtIds.clear();
                          _isMultiSelectMode = false;
                        });
                      });
                    },
                    icon: const Icon(Icons.payment, size: 14),
                    label: const Text('تسديد', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      minimumSize: const Size(0, 28),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                ],

                // زر تحديد الكل
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _selectedDebtIds.clear();
                      for (final debt in debts) {
                        if (debt.id != null) {
                          _selectedDebtIds.add(debt.id!);
                        }
                      }
                    });
                  },
                  icon: const Icon(Icons.select_all, size: 14),
                  label:
                      const Text('تحديد الكل', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    minimumSize: const Size(0, 28),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),

                // زر الحذف (فقط عندما يوجد عناصر محددة)
                if (_selectedDebtIds.isNotEmpty) ...[
                  const SizedBox(width: 6),

                  // زر الحذف
                  ElevatedButton.icon(
                    onPressed: () {
                      _deleteSelectedDebts(context, debtProvider);
                    },
                    icon: const Icon(Icons.delete, size: 14),
                    label: const Text('حذف', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      minimumSize: const Size(0, 28),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                ],

                // زر إلغاء التحديد
                IconButton(
                  onPressed: () {
                    setState(() {
                      _selectedDebtIds.clear();
                      _isMultiSelectMode = false;
                    });
                  },
                  icon:
                      Icon(Icons.close, color: Colors.grey.shade700, size: 16),
                  tooltip: 'إلغاء التحديد',
                  padding: const EdgeInsets.all(4),
                  constraints:
                      const BoxConstraints(minWidth: 28, minHeight: 28),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء شريط معلومات الديون المحددة في الأعلى
  Widget _buildTopSelectedDebtsInfo(List<Debt> allDebts) {
    // حساب إجمالي المبلغ للديون المحددة
    double totalAmount = 0;
    int selectedCount = 0;

    for (final debt in allDebts) {
      if (_selectedDebtIds.contains(debt.id)) {
        totalAmount += debt.remainingAmount;
        selectedCount++;
      }
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة المعلومات
          Icon(
            Icons.info_outline,
            color: Colors.teal.shade600,
            size: 20,
          ),
          const SizedBox(width: 8),

          // معلومات الديون المحددة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الديون المحددة: $selectedCount',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'إجمالي المبلغ: ${NumberFormatter.formatCurrency(totalAmount.toInt())}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal.shade700,
                  ),
                ),
              ],
            ),
          ),

          // أيقونة المبلغ
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.teal.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.attach_money,
              color: Colors.teal.shade600,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط الأدوات العلوي الموحد (بدون زر أنواع العرض)
  Widget _buildUnifiedTopBar(List<Debt> activeDebts) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('الكل', 'all'),
            const SizedBox(width: 8),
            if (_currentViewType == DebtCardViewType.table) ...[
              _buildFilterChip('جدول مباشر', 'table_direct'),
              const SizedBox(width: 8),
            ],
            _buildFilterChip('متأخر', 'overdue'),
            const SizedBox(width: 8),
            _buildFilterChip('قريباً', 'soon'),
            const SizedBox(width: 8),
            _buildFilterChip('اليوم', 'today'),
            const SizedBox(width: 8),
            _buildFilterChip('أمس', 'yesterday'),
            const SizedBox(width: 8),
            _buildFilterChip('هذا الأسبوع', 'thisWeek'),
            const SizedBox(width: 8),
            _buildFilterChip('هذا الشهر', 'thisMonth'),
            const SizedBox(width: 8),
            _buildFilterChip('شهر السادس', 'june'),
          ],
        ),
      ),
    );
  }

  // بناء أعمدة الجدول
  List<DataColumn> _buildTableColumns(List<Debt> debts) {
    // التحقق من وجود ديون بمبالغ متبقية (دفع جزئي)
    final hasPartialPayments = debts.any((debt) =>
        debt.remainingAmount > 0 && debt.remainingAmount < debt.amount);

    return [
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'نوع الكارت',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الكمية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المبلغ الكلي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المدفوع',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المتبقي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      // عمود الكارتات المتبقية (فقط في حالة وجود دفع جزئي)
      if (hasPartialPayments)
        const DataColumn(
          label: Expanded(
            child: Center(
              child: Text(
                'الكارتات المتبقية',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  fontFamily: 'Calibri',
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'تاريخ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الاستحقاق',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      DataColumn(
        label: Container(
          width: 200,
          alignment: Alignment.center,
          child: const Text(
            'الملاحظات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'منذ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الأيام المتبقية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    ];
  }

  // الحصول على padding الخلية حسب نوع الفلتر
  EdgeInsets _getCellPadding() {
    return _currentFilter == 'table_direct'
        ? const EdgeInsets.symmetric(horizontal: 8)
        : const EdgeInsets.symmetric(horizontal: 4, vertical: 2);
  }

  // بناء صفوف الجدول
  List<DataRow> _buildTableRows(List<Debt> debts) {
    // التحقق من وجود ديون بمبالغ متبقية (دفع جزئي)
    final hasPartialPayments = debts.any((debt) =>
        debt.remainingAmount > 0 && debt.remainingAmount < debt.amount);

    return debts.map((debt) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);

      final daysUntilDue = debtDueDate.difference(today).inDays;
      final isOverdue = daysUntilDue < 0;
      final isSoon = !isOverdue && daysUntilDue >= 0 && daysUntilDue <= 3;
      final isYesterday = debtEntryDate
          .isAtSameMomentAs(today.subtract(const Duration(days: 1)));
      final isToday = debtEntryDate.isAtSameMomentAs(today);

      // تحديد لون الصف (نفس منطق نظرة عامة للديون)
      Color rowColor = Colors.white;
      if (isOverdue) {
        rowColor = Colors.red.shade600;
      } else if (isSoon) {
        rowColor = Colors.orange.shade400;
      } else if (isToday) {
        rowColor = Colors.green.shade600;
      } else if (isYesterday) {
        rowColor = Colors.blue.shade900;
      }

      return DataRow(
        color: WidgetStateProperty.all(rowColor),
        selected: _selectedDebtIds.contains(debt.id),
        onSelectChanged: (selected) =>
            _handleTableRowSelection(debt.id, selected),
        onLongPress: () => _showDebtActionBottomSheet(debt),
        cells: [
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  _getCardTypeDisplayName(debt.cardType),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  NumberFormatter.formatNumber(debt.quantity),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  NumberFormatter.formatCurrency(debt.amount),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          // خلية المبلغ المدفوع
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  NumberFormatter.formatCurrency(
                      debt.amount - debt.remainingAmount),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.green.shade700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          // خلية المبلغ المتبقي
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  // إظهار المبلغ المتبقي فقط إذا كان هناك دفع جزئي
                  (debt.remainingAmount > 0 &&
                          debt.remainingAmount < debt.amount)
                      ? NumberFormatter.formatCurrency(debt.remainingAmount)
                      : '-',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : debt.remainingAmount > 0
                            ? Colors.red.shade700
                            : Colors.green.shade700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          // خلية الكارتات المتبقية (فقط في حالة وجود دفع جزئي)
          if (hasPartialPayments)
            DataCell(
              Container(
                alignment: Alignment.center,
                padding: _getCellPadding(),
                child: Center(
                  child: Text(
                    // إظهار الكارتات المتبقية فقط إذا كان هناك دفع جزئي
                    (debt.remainingAmount > 0 &&
                            debt.remainingAmount < debt.amount)
                        ? _calculateRemainingCards(debt).toString()
                        : '-',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: isOverdue || isYesterday || isToday
                          ? Colors.white
                          : Colors.blue.shade700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  _formatFullDateWithDay(debt.entryDate),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  softWrap: false,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  _formatDateWithDayOnly(debt.dueDate),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  softWrap: false,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              width: 200,
              alignment: Alignment.center,
              child: Center(
                child: Text(
                  debt.notes?.isNotEmpty == true ? debt.notes! : '-',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  '${_getDaysSinceEntry(debt)} يوم',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: _getCellPadding(),
              child: Center(
                child: Text(
                  '${_getDaysRemaining(debt)} يوم',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      );
    }).toList();
  }

  // دوال مساعدة للجدول
  String _getCardTypeDisplayName(String cardType) {
    // البحث في الأنواع الافتراضية أولاً
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardType,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
      try {
        final cardTypeProvider =
            Provider.of<CardTypeProvider>(context, listen: false);
        final customType = cardTypeProvider.customCardTypes.firstWhere(
          (type) => 'custom_${type.id}' == cardType,
        );
        return customType.displayName;
      } catch (e) {
        // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
        return cardType;
      }
    }
  }

  String _formatFullDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final dayName = dayNames[date.weekday % 7];
    final timeStr = _formatTimeInArabic(date);
    return '$dayName ${DateFormat('yyyy/MM/dd').format(date)} 🕐 $timeStr';
  }

  // تنسيق التاريخ مع اسم اليوم فقط (بدون وقت)
  String _formatDateWithDayOnly(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final dayName = dayNames[date.weekday % 7];
    return '$dayName ${DateFormat('yyyy/MM/dd').format(date)}';
  }

  String _formatTimeInArabic(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;
    final minuteStr = minute.toString().padLeft(2, '0');

    if (hour == 0) {
      return '12:$minuteStr منتصف الليل';
    } else if (hour < 12) {
      final hourStr = hour.toString().padLeft(2, '0');
      return '$hourStr:$minuteStr صباحاً';
    } else if (hour == 12) {
      return '12:$minuteStr ظهراً';
    } else {
      final hour12 = hour - 12;
      final hourStr = hour12.toString().padLeft(2, '0');
      return '$hourStr:$minuteStr مساءً';
    }
  }

  int _getDaysSinceEntry(Debt debt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final entryDate =
        DateTime(debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
    return today.difference(entryDate).inDays;
  }

  int _getDaysRemaining(Debt debt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDate =
        DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
    return dueDate.difference(today).inDays;
  }

  // حساب عدد الكارتات المتبقية بناءً على المبلغ المتبقي
  int _calculateRemainingCards(Debt debt) {
    if (debt.remainingAmount <= 0 || debt.quantity <= 0) {
      return 0;
    }

    // حساب قيمة الكارت الواحد (المبلغ الأصلي ÷ الكمية)
    final cardValue = debt.amount / debt.quantity;

    if (cardValue <= 0) {
      return 0;
    }

    // حساب عدد الكارتات من المبلغ المتبقي
    final remainingCards = debt.remainingAmount / cardValue;

    return remainingCards.round();
  }

  // فلترة الديون حسب الفلتر المحدد
  List<Debt> _filterDebts(List<Debt> debts) {
    if (_currentFilter == 'all' || _currentFilter == 'table_direct') {
      return debts;
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    final filteredDebts = debts.where((debt) {
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final daysUntilDue = debtDueDate.difference(today).inDays;

      switch (_currentFilter) {
        case 'overdue':
          // متأخر: الديون المتأخرة عن موعد الاستحقاق
          return daysUntilDue < 0;
        case 'soon':
          // قريباً: الديون المستحقة خلال الأيام القليلة القادمة
          // يشمل: اليوم، غداً، بعد غد، وخلال 3 أيام
          return daysUntilDue >= 0 && daysUntilDue <= 3;
        case 'today':
          // اليوم: الديون المقيدة اليوم فقط
          return debtEntryDate.isAtSameMomentAs(today);
        case 'yesterday':
          // أمس: الديون المقيدة أمس فقط
          return debtEntryDate.isAtSameMomentAs(yesterday);
        case 'thisWeek':
          // هذا الأسبوع: الديون المقيدة هذا الأسبوع
          final weekStart = today.subtract(Duration(days: today.weekday - 1));
          final weekEnd = weekStart.add(const Duration(days: 6));
          return debtEntryDate
                  .isAfter(weekStart.subtract(const Duration(days: 1))) &&
              debtEntryDate.isBefore(weekEnd.add(const Duration(days: 1)));
        case 'thisMonth':
          // هذا الشهر: الديون المقيدة هذا الشهر
          return debtEntryDate.year == today.year &&
              debtEntryDate.month == today.month;
        case 'june':
          // شهر السادس: الديون المقيدة في شهر يونيو من السنة الحالية
          return debtEntryDate.year == today.year && debtEntryDate.month == 6;
        default:
          return true;
      }
    }).toList();

    return filteredDebts;
  }

  // الحصول على اسم العرض للفلتر
  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'overdue':
        return 'متأخر';
      case 'soon':
        return 'قريباً';
      case 'today':
        return 'اليوم';
      case 'yesterday':
        return 'أمس';
      case 'thisWeek':
        return 'هذا الأسبوع';
      case 'thisMonth':
        return 'هذا الشهر';
      case 'june':
        return 'شهر السادس';
      case 'table_direct':
        return 'جدول مباشر';
      default:
        return 'الكل';
    }
  }

  // ═══════════════════════════════════════════════════════════
  // بناء الإحصائيات السريعة لقائمة ديون العميل المحدد
  // هذه الدالة خاصة بـ Customer Debts List وليس Debts Overview
  // ═══════════════════════════════════════════════════════════
  Widget _buildGeneralQuickStats(List<Debt> allDebts) {
    if (allDebts.isEmpty) return const SizedBox.shrink();

    final totalAmount =
        allDebts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);
    final overdueDebtsList = allDebts
        .where((debt) =>
            debt.dueDate.isBefore(DateTime.now()) && debt.remainingAmount > 0)
        .toList();
    final overdueDebts = overdueDebtsList.length;

    // حساب عدد الكارتات بناءً على المبلغ المتبقي مقسوماً على قيمة الكارت
    double totalCardsFromAmount = 0;
    for (final debt in allDebts) {
      if (debt.remainingAmount > 0) {
        // حساب قيمة الكارت الواحد (المبلغ الأصلي ÷ الكمية)
        final cardValue = debt.quantity > 0 ? debt.amount / debt.quantity : 0;
        if (cardValue > 0) {
          // حساب عدد الكارتات من المبلغ المتبقي
          totalCardsFromAmount += debt.remainingAmount / cardValue;
        }
      }
    }
    final totalCards = totalCardsFromAmount.round();

    // حساب أنواع الكروت
    final cardTypes = <String, int>{};
    for (final debt in allDebts) {
      final cardTypeName = _getCardTypeDisplayName(debt.cardType);
      cardTypes[cardTypeName] = (cardTypes[cardTypeName] ?? 0) + 1;
    }

    // حساب الإحصائيات الزمنية
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    int soonCount = 0;
    double soonAmount = 0;

    for (final debt in allDebts) {
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final daysUntilDue = debtDueDate.difference(today).inDays;

      if (daysUntilDue >= 0 && daysUntilDue <= 3) {
        soonCount++;
        soonAmount += debt.remainingAmount;
      }
    }

    return Container(
      margin: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الإحصائيات مع زر الطي
          Padding(
            padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
            child: Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  size: 16,
                  color: Colors.teal.shade600,
                ),
                const SizedBox(width: 6),
                Text(
                  'إحصائيات عامة',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal.shade700,
                  ),
                ),
                const Spacer(),
                // زر الطي
                InkWell(
                  onTap: () {
                    if (!mounted) return;
                    final newState = !_isStatsCollapsed;
                    setState(() {
                      _isStatsCollapsed = newState;
                    });
                    // حفظ الحالة الجديدة
                    _saveStatsCollapseState(newState);
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      _isStatsCollapsed ? Icons.expand_more : Icons.expand_less,
                      size: 18,
                      color: Colors.teal.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // الإحصائيات مع التمرير الأفقي (قابلة للطي)
          if (!_isStatsCollapsed)
            SizedBox(
              height: 120, // زيادة الارتفاع من 100 إلى 120
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(
                    horizontal: 12), // زيادة الحشو الأفقي
                child: Row(
                  children: [
                    // إجمالي المبلغ
                    _buildGeneralStatCard(
                      'إجمالي المبلغ',
                      NumberFormatter.formatNumber(totalAmount.toInt()),
                      Icons.attach_money,
                      Colors.green,
                    ),
                    const SizedBox(width: 16), // زيادة المسافة بين العدادات

                    // الكارتات المتبقية
                    _buildGeneralStatCard(
                      'الكارتات المتبقية',
                      '$totalCards',
                      Icons.credit_card,
                      Colors.blue,
                    ),
                    const SizedBox(width: 16), // زيادة المسافة بين العدادات

                    // متأخرة
                    if (overdueDebts > 0) ...[
                      _buildGeneralStatCard(
                        'متأخرة',
                        '$overdueDebts',
                        Icons.warning,
                        Colors.red,
                      ),
                      const SizedBox(width: 10),
                    ],

                    // قريباً
                    if (soonCount > 0) ...[
                      _buildGeneralStatCard(
                        'قريباً',
                        '$soonCount',
                        Icons.schedule,
                        Colors.orange,
                        subtitle:
                            NumberFormatter.formatNumber(soonAmount.toInt()),
                      ),
                      const SizedBox(width: 8),
                    ],

                    // أنواع الكروت
                    ...cardTypes.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: _buildGeneralStatCard(
                          entry.key,
                          '${entry.value}',
                          Icons.style,
                          _getCardTypeColor(entry.key),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // الحصول على لون نوع الكارت
  MaterialColor _getCardTypeColor(String cardType) {
    switch (cardType.toLowerCase()) {
      case 'زين':
        return Colors.purple;
      case 'آسيا':
        return Colors.red;
      case 'أبو الستة':
        return Colors.grey;
      case 'أبو العشرة':
        return Colors.teal;
      default:
        return Colors.indigo;
    }
  }

  // بناء بطاقة إحصائية عامة
  Widget _buildGeneralStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      width: 150, // زيادة العرض من 120 إلى 150
      padding: const EdgeInsets.all(16), // زيادة الحشو من 12 إلى 16
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.teal.shade50, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16), // زيادة الانحناء من 12 إلى 16
        border: Border.all(
            color: Colors.black.withValues(alpha: 0.15)), // حواف سوداء خفيفة
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08), // تخفيف الظل
            blurRadius: 6, // زيادة الضبابية
            offset: const Offset(0, 3), // زيادة الإزاحة
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  // بناء شريط الفلترة العلوي
  Widget _buildTopFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('الكل', 'all'),
            const SizedBox(width: 8),
            _buildFilterChip('جدول مباشر', 'table_direct'),
            const SizedBox(width: 8),
            _buildFilterChip('متأخر', 'overdue'),
            const SizedBox(width: 8),
            _buildFilterChip('قريباً', 'soon'),
            const SizedBox(width: 8),
            _buildFilterChip('اليوم', 'today'),
            const SizedBox(width: 8),
            _buildFilterChip('أمس', 'yesterday'),
            const SizedBox(width: 8),
            _buildFilterChip('هذا الأسبوع', 'thisWeek'),
            const SizedBox(width: 8),
            _buildFilterChip('هذا الشهر', 'thisMonth'),
            const SizedBox(width: 8),
            _buildFilterChip('شهر السادس', 'june'),
          ],
        ),
      ),
    );
  }

  // بناء رقاقة الفلتر
  Widget _buildFilterChip(String label, String value) {
    final isSelected = _currentFilter == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.black87,
          fontSize: 12,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        if (!mounted || _currentFilter == value) return;
        setState(() {
          _currentFilter = value;
        });
      },
      backgroundColor: Colors.white,
      selectedColor: Colors.teal.shade600,
      side: BorderSide(
        color: isSelected ? Colors.teal.shade600 : Colors.grey.shade400,
      ),
      showCheckmark: false,
    );
  }

  // دالة لبناء شريط تمييز حالة الدين
  Widget _buildDebtStatusStrip(Debt debt) {
    // تحديد لون الشريط حسب حالة الدين
    Color stripColor;
    String statusText;
    IconData statusIcon;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDate =
        DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
    final daysUntilDue = dueDate.difference(today).inDays;

    if (debt.remainingAmount <= 0) {
      stripColor = Colors.green;
      statusText = 'مسدد';
      statusIcon = Icons.check_circle;
    } else if (daysUntilDue < 0) {
      // متأخر
      stripColor = Colors.red;
      statusText = 'متأخر';
      statusIcon = Icons.warning;
    } else if (daysUntilDue == 0) {
      // مستحق اليوم
      stripColor = Colors.orange;
      statusText = 'مستحق اليوم';
      statusIcon = Icons.schedule;
    } else if (daysUntilDue <= 3) {
      // قريباً (خلال 3 أيام)
      stripColor = Colors.amber;
      statusText = 'قريباً';
      statusIcon = Icons.access_time;
    } else {
      // غير مستحق بعد
      stripColor = Colors.blue;
      statusText = 'غير مستحق';
      statusIcon = Icons.schedule_send;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: stripColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          // اسم العميل من الجهة اليسرى
          Expanded(
            flex: 2,
            child: Text(
              widget.customer.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 9,
                fontWeight: FontWeight.w700,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),

          const SizedBox(width: 6),

          // أيقونة الحالة
          Icon(
            statusIcon,
            color: Colors.white,
            size: 14,
          ),
          const SizedBox(width: 4),

          // نص الحالة
          Expanded(
            child: Text(
              statusText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w800,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // تنسيق التاريخ بالعربية
  String _formatDateArabic(DateTime? date) {
    if (date == null) return '';

    final dayFormat = DateFormat('EEEE', 'ar');
    final dateFormat = DateFormat('yyyy/MM/dd');

    return '${dayFormat.format(date)} - ${dateFormat.format(date)}';
  }

  // تنسيق الوقت بالعربية (صباحاً/مساءً)
  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;

    // تحديد الفترة
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'مساءً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  // حساب عدد الأيام منذ القيد
  int _calculateDaysSinceEntry(DateTime entryDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final entryDay = DateTime(entryDate.year, entryDate.month, entryDate.day);
    return today.difference(entryDay).inDays;
  }

  // تنسيق الأيام المتبقية
  String _formatRemainingDays(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDateDay = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final remainingDays = dueDateDay.difference(today).inDays;

    if (remainingDays < 0) {
      return '${remainingDays.abs()} متأخر';
    } else if (remainingDays == 0) {
      return 'اليوم';
    } else {
      return '$remainingDays يوم';
    }
  }

  // تحديد لون الأيام المتبقية
  Color _getRemainingDaysColor(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDateDay = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final remainingDays = dueDateDay.difference(today).inDays;

    if (remainingDays < 0) {
      return Colors.red; // متأخر
    } else if (remainingDays == 0) {
      return Colors.orange; // اليوم
    } else if (remainingDays <= 3) {
      return Colors.amber; // قريب
    } else {
      return Colors.green; // آمن
    }
  }
}
