import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:async';

import '../models/customer.dart';
import '../models/payment.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../utils/number_formatter.dart';
import 'payment_card.dart';

// أنواع عرض التسديدات
enum PaymentCardViewType {
  standard, // العرض العادي
  compact, // عرض مضغوط
  mini, // عرض مصغر (مثل قائمة الديون)
  detailed, // عرض مفصل
  grid, // عرض شبكي
  timeline, // عرض زمني
}

class PaymentsTab extends StatefulWidget {
  const PaymentsTab({
    super.key,
    required this.customer,
    this.isSelectionMode = false,
    this.selectedPaymentIds = const {},
    this.onSelectionChanged,
    this.currentViewType,
    this.filteredPayments,
  });
  final Customer customer;
  final List<Payment>? filteredPayments;
  final bool isSelectionMode;
  final Set<int> selectedPaymentIds;
  final Function(int paymentId, bool isSelected)? onSelectionChanged;
  final PaymentCardViewType? currentViewType;

  @override
  State<PaymentsTab> createState() => _PaymentsTabState();
}

class _PaymentsTabState extends State<PaymentsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};
  Timer? _timer;

  @override
  void initState() {
    super.initState();

    // Load payments when tab is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerPayments(widget.customer.id!);
    });

    // تحديث العداد كل دقيقة لدقة العرض (معطل مؤقتاً)
    // _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
    //   if (mounted) {
    //     setState(() {});
    //   }
    // });
  }

  @override
  void didUpdateWidget(PaymentsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  // دالة محسنة للتعامل مع تبديل التحديد
  void _handleToggleSelection(int paymentId) {
    bool needsUpdate = false;

    if (!_isSelectionMode) {
      // بدء وضع التحديد المتعدد
      _isSelectionMode = true;
      _selectedPaymentIds.clear();
      _selectedPaymentIds.add(paymentId);
      needsUpdate = true;
    } else {
      // تبديل التحديد في وضع التحديد المتعدد
      if (_selectedPaymentIds.contains(paymentId)) {
        _selectedPaymentIds.remove(paymentId);
        // إنهاء وضع التحديد إذا لم تعد هناك عناصر محددة
        if (_selectedPaymentIds.isEmpty) {
          _isSelectionMode = false;
        }
        needsUpdate = true;
      } else {
        _selectedPaymentIds.add(paymentId);
        needsUpdate = true;
      }
    }

    // تحديث الواجهة فقط إذا كان هناك تغيير فعلي
    if (needsUpdate && mounted) {
      setState(() {});
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تحسين: تجنب إعادة التحميل غير الضروري
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id &&
        !debtProvider.isLoading &&
        debtProvider.payments
            .where((p) => p.customerId == widget.customer.id)
            .isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          debtProvider.loadCustomerPayments(widget.customer.id!);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // Get payments for this customer (filtered or all)
        List<Payment> customerPayments;

        if (widget.filteredPayments != null &&
            widget.filteredPayments!.isNotEmpty) {
          // استخدام التسديدات المفلترة
          customerPayments = widget.filteredPayments!;
          // استخدام التسديدات المفلترة: ${customerPayments.length}
        } else {
          // استخدام جميع التسديدات للعميل
          customerPayments = debtProvider.payments
              .where((payment) => payment.customerId == widget.customer.id)
              .toList();
          // استخدام جميع التسديدات: ${customerPayments.length}
        }

        if (customerPayments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.payment_outlined, size: 80, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد تسديدات',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر التسديدات هنا بعد إجراء عمليات الدفع',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // الإحصائيات السريعة
            if (!_isSelectionMode && customerPayments.isNotEmpty)
              _buildPaymentStatusStrip(customerPayments),

            // شريط معلومات التحديد في الأعلى (عند التحديد)
            if (_isSelectionMode && _selectedPaymentIds.isNotEmpty)
              _buildTopSelectionInfo(),

            // Action buttons
            Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // Top row - Selection and Statistics buttons
                  if (customerPayments.isNotEmpty) ...[
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _isSelectionMode
                          ? Column(
                              key: const ValueKey('selection_mode'),
                              children: [
                                // شريط أدوات الحذف المحسن
                                _buildTopDeleteToolbar(),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                    const SizedBox(height: 8),
                  ],
                ],
              ),
            ),

            // Payments List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await debtProvider.loadCustomerPayments(widget.customer.id!);
                },
                child: buildPaymentsView(customerPayments, debtProvider),
              ),
            ),

            // أدوات إرجاع التسديد في الأسفل (عند التحديد)
            if (_isSelectionMode && _selectedPaymentIds.isNotEmpty)
              _buildBottomReverseToolbar(),
          ],
        );
      },
    );
  }

  // بناء شريط أدوات الحذف المحسن في الأعلى
  Widget _buildTopDeleteToolbar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: Colors.red.shade100,
        ),
      ),
      child: Row(
        children: [
          // أيقونة ونص توضيحي
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.delete_outline,
              color: Colors.red.shade600,
              size: 14,
            ),
          ),
          const SizedBox(width: 8),

          // زر تحديد الكل
          _buildModernDeleteButton(
            onPressed: () => selectAllPayments(),
            icon: Icons.select_all,
            label: 'تحديد الكل',
            isPrimary: false,
            isEnabled: true,
          ),

          const Spacer(),

          // أزرار الحذف المحسنة
          Row(
            children: [
              // زر حذف المحدد
              _buildModernDeleteButton(
                onPressed: _selectedPaymentIds.isEmpty
                    ? null
                    : () => deleteSelectedPayments(),
                icon: Icons.delete_outline,
                label: 'حذف (${_selectedPaymentIds.length})',
                isPrimary: true,
                isEnabled: _selectedPaymentIds.isNotEmpty,
              ),
              const SizedBox(width: 6),

              // زر حذف الكل
              _buildModernDeleteButton(
                onPressed: () => deleteAllPayments(),
                icon: Icons.delete_sweep_outlined,
                label: 'حذف الكل',
                isPrimary: false,
                isEnabled: true,
              ),
              const SizedBox(width: 8),

              // زر الإغلاق
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => toggleSelectionMode(),
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.close,
                      color: Colors.grey.shade600,
                      size: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء زر حذف حديث ومحسن
  Widget _buildModernDeleteButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required bool isPrimary,
    required bool isEnabled,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? onPressed : null,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: isEnabled
                ? (isPrimary ? Colors.red.shade600 : Colors.red.shade50)
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
            border: isPrimary
                ? null
                : Border.all(
                    color:
                        isEnabled ? Colors.red.shade200 : Colors.grey.shade300,
                  ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 12,
                color: isEnabled
                    ? (isPrimary ? Colors.white : Colors.red.shade600)
                    : Colors.grey.shade400,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: isEnabled
                      ? (isPrimary ? Colors.white : Colors.red.shade600)
                      : Colors.grey.shade400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء شريط معلومات التحديد في الأعلى
  Widget _buildTopSelectionInfo() {
    final selectedCount = _selectedPaymentIds.length;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.9),
            Colors.grey.shade100.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.blue.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'تم تحديد $selectedCount تسديد',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط أدوات إرجاع التسديد في الأسفل (احترافي بسيط)
  Widget _buildBottomReverseToolbar() {
    return SafeArea(
      child: Container(
        margin: const EdgeInsets.only(
            left: 8, right: 8, top: 6, bottom: 16), // زيادة المسافة من الأسفل
        padding: const EdgeInsets.symmetric(
            horizontal: 12, vertical: 12), // زيادة الحشو العمودي
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.orange.shade100,
          ),
        ),
        child: Row(
          children: [
            // أيقونة ونص توضيحي
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.undo_outlined,
                color: Colors.orange.shade600,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),

            Text(
              'أدوات الإرجاع',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),

            const Spacer(),

            // أزرار الإرجاع المحسنة
            Row(
              children: [
                // زر إرجاع المحدد
                _buildModernButton(
                  onPressed: _selectedPaymentIds.isEmpty
                      ? null
                      : () => reverseSelectedPayments(),
                  icon: Icons.undo_outlined,
                  label: 'إرجاع (${_selectedPaymentIds.length})',
                  isPrimary: true,
                  isEnabled: _selectedPaymentIds.isNotEmpty,
                ),
                const SizedBox(width: 6),

                // زر إرجاع الكل
                _buildModernButton(
                  onPressed: () => reverseAllPayments(),
                  icon: Icons.restore_outlined,
                  label: 'إرجاع الكل',
                  isPrimary: false,
                  isEnabled: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء زر حديث ومحسن
  Widget _buildModernButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required bool isPrimary,
    required bool isEnabled,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? onPressed : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isEnabled
                ? (isPrimary ? Colors.orange.shade600 : Colors.orange.shade50)
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(10),
            border: isPrimary
                ? null
                : Border.all(
                    color: isEnabled
                        ? Colors.orange.shade200
                        : Colors.grey.shade300,
                  ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 14,
                color: isEnabled
                    ? (isPrimary ? Colors.white : Colors.orange.shade600)
                    : Colors.grey.shade400,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: isEnabled
                      ? (isPrimary ? Colors.white : Colors.orange.shade600)
                      : Colors.grey.shade400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء الإحصائيات السريعة مع التمرير الأفقي
  Widget _buildPaymentStatusStrip(List customerPayments) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisMonthStart = DateTime(today.year, today.month);

    // حساب التسديدات والمبالغ حسب الفترات
    int todayPayments = 0;
    int yesterdayPayments = 0;
    int weekPayments = 0;
    int monthPayments = 0;
    double todayAmount = 0;
    double yesterdayAmount = 0;
    double weekAmount = 0;
    double monthAmount = 0;
    double totalAmount = 0;
    double totalPaidQuantity = 0; // الكمية المسددة حسب النسبة
    double totalOriginalQuantity = 0; // إجمالي الكمية الأصلية

    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final processedDebts = <int>{}; // لتجنب العد المكرر للديون

    for (final payment in customerPayments) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );

      totalAmount += payment.amount;

      // البحث عن الدين للحصول على الكمية المسددة حسب النسبة
      final debts = debtProvider.debts.where((d) => d.id == payment.debtId);
      if (debts.isNotEmpty && !processedDebts.contains(payment.debtId)) {
        final debt = debts.first;
        processedDebts.add(payment.debtId);

        // إضافة الكمية الأصلية
        totalOriginalQuantity += debt.quantity;

        // حساب النسبة المسددة من هذا الدين
        final paidRatio = debt.paidAmount / debt.amount;
        // حساب الكمية المسددة بناءً على النسبة
        final paidQuantityFromThisDebt = debt.quantity * paidRatio;
        totalPaidQuantity += paidQuantityFromThisDebt;
      }

      if (paymentDate.isAtSameMomentAs(today)) {
        todayPayments++;
        todayAmount += payment.amount;
      } else if (paymentDate.isAtSameMomentAs(yesterday)) {
        yesterdayPayments++;
        yesterdayAmount += payment.amount;
      }

      // حساب تسديدات الأسبوع (آخر 7 أيام باستثناء اليوم والأمس)
      final thisWeek = today.subtract(const Duration(days: 7));
      final isInWeekRange = paymentDate.isAfter(thisWeek);
      final isNotTodayOrYesterday = !paymentDate.isAtSameMomentAs(today) &&
          !paymentDate.isAtSameMomentAs(yesterday);

      if (isInWeekRange && isNotTodayOrYesterday) {
        weekPayments++;
        weekAmount += payment.amount;
      }

      if (paymentDate
          .isAfter(thisMonthStart.subtract(const Duration(days: 1)))) {
        monthPayments++;
        monthAmount += payment.amount;
      }
    }

    return Container(
      margin: const EdgeInsets.only(left: 8, right: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الإحصائيات
          Padding(
            padding:
                const EdgeInsets.only(left: 8, right: 8, top: 2, bottom: 2),
            child: Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  size: 14,
                  color: Colors.teal.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  'إحصائيات سريعة',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.teal.shade700,
                  ),
                ),
              ],
            ),
          ),

          // الإحصائيات مع التمرير الأفقي
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(left: 8, right: 8),
            child: Row(
              children: [
                // إجمالي التسديدات
                buildQuickStatCard(
                  'إجمالي التسديدات',
                  '${customerPayments.length}',
                  Icons.payment,
                  Colors.teal.shade600,
                  subtitle: NumberFormatter.formatNumber(totalAmount.toInt()),
                ),
                const SizedBox(width: 8),

                // إجمالي الكارتات المسددة
                buildQuickStatCard(
                  'الكارتات المسددة',
                  totalPaidQuantity.round().toString(),
                  Icons.credit_card,
                  Colors.blue.shade600,
                  subtitle: 'من إجمالي المدفوع',
                ),
                const SizedBox(width: 8),

                // نسبة التسديد
                buildQuickStatCard(
                  'نسبة التسديد',
                  '${totalOriginalQuantity > 0 ? ((totalPaidQuantity / totalOriginalQuantity) * 100).toStringAsFixed(0) : "0"}%',
                  Icons.pie_chart,
                  Colors.purple.shade600,
                  subtitle: 'من الكارتات',
                ),
                const SizedBox(width: 8),

                // تسديدات اليوم
                if (todayPayments > 0)
                  buildQuickStatCard(
                    'اليوم',
                    '$todayPayments',
                    Icons.today,
                    Colors.green.shade600,
                    subtitle: NumberFormatter.formatNumber(todayAmount.toInt()),
                  ),
                if (todayPayments > 0) const SizedBox(width: 8),

                // تسديدات أمس
                if (yesterdayPayments > 0)
                  buildQuickStatCard(
                    'أمس',
                    '$yesterdayPayments',
                    Icons.history,
                    Colors.orange.shade600,
                    subtitle:
                        NumberFormatter.formatNumber(yesterdayAmount.toInt()),
                  ),
                if (yesterdayPayments > 0) const SizedBox(width: 8),

                // تسديدات الأسبوع
                if (weekPayments > 0)
                  buildQuickStatCard(
                    'هذا الأسبوع',
                    '$weekPayments',
                    Icons.date_range,
                    Colors.purple.shade600,
                    subtitle: NumberFormatter.formatNumber(weekAmount.toInt()),
                  ),
                if (weekPayments > 0) const SizedBox(width: 8),

                // تسديدات الشهر
                if (monthPayments > 0)
                  buildQuickStatCard(
                    'هذا الشهر',
                    '$monthPayments',
                    Icons.calendar_month,
                    Colors.indigo.shade600,
                    subtitle: NumberFormatter.formatNumber(monthAmount.toInt()),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية سريعة
  Widget buildQuickStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // الأيقونة والقيمة
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  icon,
                  size: 14,
                  color: color,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                value,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
          const SizedBox(height: 4),

          // العنوان
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),

          // النص الفرعي
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  void toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedPaymentIds.clear();
      }
    });
  }

  void selectAllPayments() {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    setState(() {
      _selectedPaymentIds.clear();
      for (final payment in customerPayments) {
        if (payment.id != null) {
          _selectedPaymentIds.add(payment.id!);
        }
      }
    });
  }

  void deleteSelectedPayments() {
    if (_selectedPaymentIds.isEmpty) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.white,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 450),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.delete_forever,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'تأكيد حذف التسديدات',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد من حذف ${_selectedPaymentIds.length} تسديد محدد نهائياً؟',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          final debtProvider = Provider.of<DebtProvider>(
                            context,
                            listen: false,
                          );

                          final selectedCount = _selectedPaymentIds.length;
                          for (final paymentId in _selectedPaymentIds) {
                            await debtProvider.deletePayment(paymentId);
                          }

                          if (mounted && context.mounted) {
                            Navigator.pop(context);
                            setState(() {
                              _selectedPaymentIds.clear();
                              _isSelectionMode = false;
                            });

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('تم حذف $selectedCount تسديد نهائياً'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted && context.mounted) {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('حدث خطأ: ${e.toString()}'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'حذف نهائياً',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void deleteAllPayments() {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    if (customerPayments.isEmpty) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.white,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 450),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.delete_sweep,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'حذف جميع التسديدات',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد من حذف جميع التسديدات (${customerPayments.length} تسديد) نهائياً؟',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          final totalCount = customerPayments.length;
                          for (final payment in customerPayments) {
                            if (payment.id != null) {
                              await debtProvider.deletePayment(payment.id!);
                            }
                          }

                          if (mounted && context.mounted) {
                            Navigator.pop(context);
                            setState(() {
                              _selectedPaymentIds.clear();
                              _isSelectionMode = false;
                            });

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('تم حذف $totalCount تسديد نهائياً'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted && context.mounted) {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('حدث خطأ: ${e.toString()}'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'حذف الكل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة لبناء بطاقات التصنيف الزمني
  Widget buildTimeClassificationCards(List customerPayments, debtProvider) {
    // تنظيم التسديدات حسب الفترات الزمنية
    final organizedPayments = organizePaymentsByDate(customerPayments);

    if (organizedPayments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: organizedPayments.entries.map((entry) {
        final category = entry.key;
        final payments = entry.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة عنوان الفئة الزمنية (مثل بطاقات الديون)
            Container(
              width: double.infinity,
              margin:
                  const EdgeInsets.only(bottom: 4, top: 16, left: 8, right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: getPaymentCategoryColors(category),
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: getPaymentCategoryColors(category)[0]
                        .withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // أيقونة الفئة
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      getPaymentCategoryIcon(category),
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),

                  // اسم الفئة
                  Expanded(
                    child: Text(
                      category,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // إحصائيات الفئة - الكمية والمبلغ جنباً إلى جنب
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // الكمية - بطاقة احترافية
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'الكمية:',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '${getTotalQuantity(payments)}',
                              style: const TextStyle(
                                color: Colors.black87,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(width: 16), // فاصل بين الكمية والمبلغ

                      // المبلغ - بطاقة احترافية
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'المبلغ:',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              formatCurrencyWithoutSymbol(
                                  getTotalAmount(payments)),
                              style: const TextStyle(
                                color: Colors.black87,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // بطاقة تصنيف أنواع الكروت
            _buildCardTypesClassificationCard(payments),
            const SizedBox(height: 8),

            // بطاقات التسديدات مع بطاقة التاريخ فوق كل واحدة
            buildCategoryPaymentsList(payments, debtProvider, category),

            const SizedBox(height: 4),
          ],
        );
      }).toList(),
    );
  }

  // دالة لتنظيم التسديدات حسب التاريخ - نسخة جديدة مبسطة
  Map<String, List> organizePaymentsByDate(List customerPayments) {
    final Map<String, List> organized = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // حساب آخر 7 أيام (نفس منطق قائمة الديون)
    final thisWeek = today.subtract(const Duration(days: 7));

    for (final payment in customerPayments) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );

      String category;

      // تصنيف بسيط ومباشر
      if (paymentDate.isAtSameMomentAs(today)) {
        category = 'اليوم';
      } else if (paymentDate.isAtSameMomentAs(yesterday)) {
        category = 'الأمس';
      } else if (paymentDate.isAfter(thisWeek) &&
          !paymentDate.isAtSameMomentAs(today) &&
          !paymentDate.isAtSameMomentAs(yesterday)) {
        category = 'هذا الأسبوع';
      } else if (paymentDate.year == today.year &&
          paymentDate.month == today.month) {
        category = 'هذا الشهر';
      } else {
        // تصنيف حسب الشهر
        switch (paymentDate.month) {
          case 1:
            category = 'شهر الواحد';
            break;
          case 2:
            category = 'شهر الثاني';
            break;
          case 3:
            category = 'شهر الثالث';
            break;
          case 4:
            category = 'شهر الرابع';
            break;
          case 5:
            category = 'شهر الخامس';
            break;
          case 6:
            category = 'شهر السادس';
            break;
          case 7:
            category = 'شهر السابع';
            break;
          case 8:
            category = 'شهر الثامن';
            break;
          case 9:
            category = 'شهر التاسع';
            break;
          case 10:
            category = 'شهر العاشر';
            break;
          case 11:
            category = 'شهر الحادي عشر';
            break;
          case 12:
            category = 'شهر الثاني عشر';
            break;
          default:
            category = 'سابقاً';
        }
      }

      organized.putIfAbsent(category, () => []);
      organized[category]!.add(payment);
    }

    // ترتيب الفئات
    final orderedCategories = [
      'اليوم',
      'الأمس',
      'هذا الأسبوع',
      'هذا الشهر',
      'شهر الواحد',
      'شهر الثاني',
      'شهر الثالث',
      'شهر الرابع',
      'شهر الخامس',
      'شهر السادس',
      'شهر السابع',
      'شهر الثامن',
      'شهر التاسع',
      'شهر العاشر',
      'شهر الحادي عشر',
      'شهر الثاني عشر',
      'سابقاً',
    ];

    final Map<String, List> orderedResult = {};
    for (final category in orderedCategories) {
      if (organized.containsKey(category)) {
        orderedResult[category] = organized[category]!;
      }
    }

    return orderedResult;
  }

  // دالة الحصول على ألوان الفئة الزمنية للتسديدات
  List<Color> getPaymentCategoryColors(String category) {
    switch (category) {
      case 'اليوم':
        return [Colors.green.shade500, Colors.green.shade700];
      case 'الأمس':
        return [const Color(0xFF1A237E), const Color(0xFF0D1B69)];
      case 'هذا الأسبوع':
        return [Colors.orange.shade500, Colors.orange.shade700];
      case 'هذا الشهر':
        return [Colors.purple.shade500, Colors.purple.shade700];
      case 'شهر الواحد':
        return [Colors.blue.shade700, Colors.blue.shade900];
      case 'شهر الثاني':
        return [Colors.pink.shade300, Colors.pink.shade500];
      case 'شهر الثالث':
        return [Colors.green.shade600, Colors.green.shade800];
      case 'شهر الرابع':
        return [Colors.amber.shade600, Colors.amber.shade800];
      case 'شهر الخامس':
        return [Colors.red.shade400, Colors.red.shade600];
      case 'شهر السادس':
        return [Colors.indigo.shade400, Colors.indigo.shade600];
      case 'شهر السابع':
        return [Colors.deepOrange.shade400, Colors.deepOrange.shade600];
      case 'شهر الثامن':
        return [Colors.teal.shade500, Colors.teal.shade700];
      case 'شهر التاسع':
        return [Colors.cyan.shade600, Colors.cyan.shade800];
      case 'شهر العاشر':
        return [Colors.deepPurple.shade400, Colors.deepPurple.shade600];
      case 'شهر الحادي عشر':
        return [Colors.lightBlue.shade600, Colors.lightBlue.shade800];
      case 'شهر الثاني عشر':
        return [Colors.red.shade700, Colors.red.shade900];
      case 'سابقاً':
        return [Colors.brown.shade500, Colors.brown.shade700];
      default:
        return [Colors.grey.shade500, Colors.grey.shade700];
    }
  }

  // دالة الحصول على أيقونة الفئة الزمنية للتسديدات
  IconData getPaymentCategoryIcon(String category) {
    switch (category) {
      case 'اليوم':
        return Icons.today;
      case 'الأمس':
        return Icons.schedule;
      case 'هذا الأسبوع':
        return Icons.date_range;
      case 'هذا الشهر':
        return Icons.calendar_month;
      case 'شهر الواحد':
        return Icons.filter_1;
      case 'شهر الثاني':
        return Icons.filter_2;
      case 'شهر الثالث':
        return Icons.filter_3;
      case 'شهر الرابع':
        return Icons.filter_4;
      case 'شهر الخامس':
        return Icons.filter_5;
      case 'شهر السادس':
        return Icons.filter_6;
      case 'شهر السابع':
        return Icons.filter_7;
      case 'شهر الثامن':
        return Icons.filter_8;
      case 'شهر التاسع':
        return Icons.filter_9;
      case 'شهر العاشر':
        return Icons.filter_9_plus;
      case 'شهر الحادي عشر':
        return Icons.filter_9_plus;
      case 'شهر الثاني عشر':
        return Icons.filter_9_plus;
      case 'سابقاً':
        return Icons.history;
      default:
        return Icons.payment;
    }
  }

  // دالة حساب المبلغ الإجمالي
  double getTotalAmount(List payments) {
    return payments.fold<double>(0, (sum, payment) => sum + payment.amount);
  }

  // دالة حساب الكمية الإجمالية
  int getTotalQuantity(List payments) {
    int totalQuantity = 0;
    for (final payment in payments) {
      // البحث عن الدين المرتبط بالتسديد للحصول على الكمية
      final debts = Provider.of<DebtProvider>(context, listen: false)
          .debts
          .where((d) => d.id == payment.debtId);
      if (debts.isNotEmpty) {
        totalQuantity += debts.first.quantity;
      }
    }
    return totalQuantity;
  }

  // دالة لتنسيق العملة بدون رمز العملة
  String formatCurrencyWithoutSymbol(double amount) {
    final formatter = NumberFormat('#,###', 'en_US');
    return formatter.format(amount.round());
  }

  // إرجاع التسديدات المحددة
  void reverseSelectedPayments() async {
    if (_selectedPaymentIds.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.undo_outlined, color: Colors.orange.shade600),
            const SizedBox(width: 8),
            const Text(
              'إرجاع التسديدات المحددة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من إرجاع التسديدات المحددة (${_selectedPaymentIds.length} تسديد)؟\n\nسيتم إرجاع هذه التسديدات إلى قائمة الديون.',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          OutlinedButton(
            onPressed: () => Navigator.pop(context, false),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.black87,
              backgroundColor: Colors.white,
              side: BorderSide(color: Colors.grey.shade300),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إرجاع',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final debtProvider = Provider.of<DebtProvider>(
          context,
          listen: false,
        );

        final selectedCount = _selectedPaymentIds.length;
        for (final paymentId in _selectedPaymentIds) {
          await debtProvider.reversePayment(paymentId);
        }

        if (mounted && context.mounted) {
          setState(() {
            _selectedPaymentIds.clear();
            _isSelectionMode = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إرجاع $selectedCount تسديد إلى قائمة الديون'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // إرجاع جميع التسديدات
  void reverseAllPayments() async {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id!)
        .toList();

    if (customerPayments.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.restore_outlined, color: Colors.orange.shade600),
            const SizedBox(width: 8),
            const Text(
              'إرجاع جميع التسديدات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من إرجاع جميع التسديدات (${customerPayments.length} تسديد)؟\n\nسيتم إرجاع جميع التسديدات إلى قائمة الديون.',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          OutlinedButton(
            onPressed: () => Navigator.pop(context, false),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.black87,
              backgroundColor: Colors.white,
              side: BorderSide(color: Colors.grey.shade300),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إرجاع الكل',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final debtProvider = Provider.of<DebtProvider>(
          context,
          listen: false,
        );

        final totalCount = customerPayments.length;
        for (final payment in customerPayments) {
          if (payment.id != null) {
            await debtProvider.reversePayment(payment.id!);
          }
        }

        if (mounted && context.mounted) {
          setState(() {
            _selectedPaymentIds.clear();
            _isSelectionMode = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إرجاع $totalCount تسديد إلى قائمة الديون'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // دالة لتنسيق تاريخ ووقت آخر عملية تسديد
  String formatLastPaymentDateTime(List<dynamic> payments) {
    if (payments.isEmpty) return '';

    // العثور على آخر تسديد (الأحدث)
    dynamic latestPayment = payments.first;
    for (final payment in payments) {
      if (payment.paymentDate.isAfter(latestPayment.paymentDate)) {
        latestPayment = payment;
      }
    }

    final paymentDate = latestPayment.paymentDate;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final paymentDay =
        DateTime(paymentDate.year, paymentDate.month, paymentDate.day);

    // تحديد اسم اليوم
    String dayName;
    if (paymentDay == today) {
      dayName = 'اليوم';
    } else if (paymentDay == yesterday) {
      dayName = 'أمس';
    } else {
      final weekdays = [
        'الأحد',
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت'
      ];
      dayName = weekdays[paymentDate.weekday % 7];
    }

    // تنسيق التاريخ
    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');
    final formattedDate = dateFormat.format(paymentDate);

    // تنسيق الوقت مع فترة اليوم
    final hour = paymentDate.hour;
    final minute = paymentDate.minute;
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'منتصف الليل';
    } else if (hour < 6) {
      displayHour = hour;
      period = 'فجراً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'ظهراً';
    } else if (hour < 18) {
      displayHour = hour - 12;
      period = 'بعد الظهر';
    } else if (hour < 21) {
      displayHour = hour - 12;
      period = 'مساءً';
    } else {
      displayHour = hour - 12;
      period = 'ليلاً';
    }

    final formattedTime =
        '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';

    return 'آخر تسديد: $dayName، $formattedDate - $formattedTime';
  }

  // دالة لبناء قائمة بطاقات التسديدات تحت كل فئة
  Widget buildCategoryPaymentsList(
      List payments, debtProvider, String category) {
    if (payments.isEmpty) {
      return const SizedBox.shrink();
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding:
          const EdgeInsets.symmetric(horizontal: 4), // تقليل المسافة الأفقية
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // بطاقتين في كل صف
        crossAxisSpacing: 4.0, // مسافة أفقية أصغر جداً بين البطاقات
        mainAxisSpacing:
            16.0, // مسافة عمودية كبيرة جداً بين الصفوف لإضافة بطاقات
        childAspectRatio:
            0.4, // نسبة محسنة لجعل البطاقة أطول لاحتواء الأزرار بشكل صحيح
      ),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        final payment = payments[index];
        final debts = debtProvider.debts.where((d) => d.id == payment.debtId);
        final debt = debts.isNotEmpty ? debts.first : null;

        return PaymentCard(
          payment: payment,
          debt: debt,
          customer: widget.customer,
          isMiniView: true,
          categoryColors: getPaymentCategoryColors(category),
          paymentCount: index + 1,
          totalPayments: payments.length,
          isSelected: _selectedPaymentIds.contains(payment.id),
          isSelectionMode: _isSelectionMode,
          onToggleSelection: () => _handleToggleSelection(payment.id!),
        );
      },
    );
  }

  // دالة لبناء عرض التسديدات حسب النوع المحدد
  Widget buildPaymentsView(List customerPayments, debtProvider) {
    if (customerPayments.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد تسديدات',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    // عرض التسديدات مع بطاقات التصنيف الزمني
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: buildTimeClassificationCards(customerPayments, debtProvider),
    );
  }

  // بناء بطاقة تصنيف أنواع الكروت للتسديدات
  Widget _buildCardTypesClassificationCard(List payments) {
    // تجميع التسديدات حسب نوع الكارت
    final cardTypeGroups = <String, Map<String, dynamic>>{};

    // الحصول على DebtProvider للوصول إلى الديون
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);

    for (final payment in payments) {
      // البحث عن الدين المرتبط بالتسديد للحصول على نوع الكارت وعدد الكروت
      try {
        final debt = debtProvider.debts.firstWhere(
          (d) => d.id == payment.debtId,
        );

        final cardTypeName = _getCardTypeDisplayName(debt.cardType);
        if (!cardTypeGroups.containsKey(cardTypeName)) {
          cardTypeGroups[cardTypeName] = {
            'count': 0,
            'totalAmount': 0.0,
            'totalCards': 0,
          };
        }

        cardTypeGroups[cardTypeName]!['count'] += 1;
        cardTypeGroups[cardTypeName]!['totalAmount'] += payment.amount;

        // حساب عدد الكروت بناءً على نسبة التسديد
        if (payment.type == PaymentType.full) {
          // تسديد كامل = جميع كروت الدين
          cardTypeGroups[cardTypeName]!['totalCards'] += debt.quantity;
        } else {
          // تسديد جزئي = حساب عدد الكروت بناءً على نسبة المبلغ
          final cardCount =
              (debt.quantity * (payment.amount / debt.amount)).round();
          cardTypeGroups[cardTypeName]!['totalCards'] += cardCount;
        }
      } catch (e) {
        // إذا لم يتم العثور على الدين، استخدم نوع افتراضي
        const defaultCardType = 'غير محدد';
        if (!cardTypeGroups.containsKey(defaultCardType)) {
          cardTypeGroups[defaultCardType] = {
            'count': 0,
            'totalAmount': 0.0,
            'totalCards': 0,
          };
        }

        cardTypeGroups[defaultCardType]!['count'] += 1;
        cardTypeGroups[defaultCardType]!['totalAmount'] += payment.amount;
        cardTypeGroups[defaultCardType]!['totalCards'] += 1; // افتراضي
      }
    }

    // إذا لم توجد أنواع كروت، لا تعرض البطاقة
    if (cardTypeGroups.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان البطاقة
          Row(
            children: [
              Icon(
                Icons.category,
                color: Colors.green.shade600,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'تصنيف أنواع الكروت',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${cardTypeGroups.length} نوع',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.green.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // عرض أنواع الكروت في صفوف
          Wrap(
            spacing: 8,
            runSpacing: 6,
            children: cardTypeGroups.entries.map((entry) {
              final cardTypeName = entry.key;
              final data = entry.value;
              final count = data['count'] as int;
              final totalAmount = data['totalAmount'] as double;
              final totalCards = data['totalCards'] as int;

              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200, width: 0.5),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.payment,
                      size: 12,
                      color: Colors.green.shade600,
                    ),
                    const SizedBox(width: 6),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          cardTypeName,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        Text(
                          '$count تسديد • $totalCards كارت',
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          NumberFormatter.formatCurrency(totalAmount.toInt()),
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // دالة للحصول على اسم نوع الكارت الصحيح
  String _getCardTypeDisplayName(String cardType) {
    final cardTypeProvider =
        Provider.of<CardTypeProvider>(context, listen: false);

    // البحث في الأنواع الافتراضية أولاً
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardType,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
      try {
        final customType = cardTypeProvider.customCardTypes.firstWhere(
          (type) => 'custom_${type.id}' == cardType,
        );
        return customType.displayName;
      } catch (e) {
        // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
        return cardType;
      }
    }
  }
}
