import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../widgets/debt_card.dart';

import '../providers/card_type_provider.dart';
import '../providers/debt_provider.dart';
import '../database/database_helper.dart';

// أنواع عرض البطاقات
enum DailySalesViewType {
  standard('عادي'),
  compact('مضغوط');

  const DailySalesViewType(this.label);
  final String label;
}

class DailySalesDebtsScreen extends StatefulWidget {
  const DailySalesDebtsScreen({
    super.key,
    required this.isToday,
    required this.title,
  });

  final bool isToday;
  final String title;

  @override
  State<DailySalesDebtsScreen> createState() => _DailySalesDebtsScreenState();
}

class _DailySalesDebtsScreenState extends State<DailySalesDebtsScreen>
    with TickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Debt> _debts = [];
  Map<int, Customer> _customers = {};
  bool _isLoading = true;
  DailySalesViewType _currentViewType = DailySalesViewType.standard;
  bool _isStatisticsExpanded = false;
  late AnimationController _statisticsAnimationController;
  late Animation<double> _statisticsAnimation;

  // متغيرات شريط الأيام
  late DateTime _selectedDate;
  final List<DateTime> _monthDays = [];
  final ScrollController _daysScrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // تحديد التاريخ حسب نوع التبويب
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    if (widget.isToday) {
      _selectedDate = today;
      print('DEBUG: تبويب اليوم - التاريخ المحدد: ${_selectedDate.toString()}');
    } else {
      // الأمس
      _selectedDate = today.subtract(const Duration(days: 1));
      print('DEBUG: تبويب الأمس - التاريخ المحدد: ${_selectedDate.toString()}');
    }

    _statisticsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _statisticsAnimation = CurvedAnimation(
      parent: _statisticsAnimationController,
      curve: Curves.easeInOut,
    );
    _generateMonthDays();
    _loadDebts();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // التأكد من التمرير إلى اليوم الحالي عند تغيير التبعيات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _scrollToCurrentDay();
      }
    });
  }

  @override
  void dispose() {
    _statisticsAnimationController.dispose();
    _daysScrollController.dispose();
    super.dispose();
  }

  // إنشاء قائمة أيام الشهر الحالي
  void _generateMonthDays() {
    final now = DateTime.now();
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

    _monthDays.clear();
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      _monthDays.add(DateTime(now.year, now.month, day));
    }

    // التمرير التلقائي إلى التاريخ المحدد بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDate(_selectedDate);
    });
  }

  // التمرير إلى التاريخ المحدد
  void _scrollToCurrentDay() {
    if (_monthDays.isEmpty) return;

    final targetDate = _selectedDate;
    final currentDayIndex = _monthDays.indexWhere((date) =>
        date.day == targetDate.day &&
        date.month == targetDate.month &&
        date.year == targetDate.year);

    if (currentDayIndex != -1 && _daysScrollController.hasClients) {
      // حساب دقيق لعرض العناصر والمسافات (متطابق مع التصميم الفعلي)
      const itemWidth = 60.0; // عرض كل عنصر يوم (من Container width)
      const horizontalPadding = 8.0; // الحشو الأفقي للقائمة
      const itemMargin = 8.0; // المسافة بين العناصر (4 من كل جانب = 8 إجمالي)

      // حساب الموضع الدقيق للعنصر
      final itemCenterPosition =
          (currentDayIndex * (itemWidth + itemMargin)) + (itemWidth / 2);

      // حساب عرض الشاشة الفعلي
      final screenWidth = MediaQuery.of(context).size.width;
      final screenCenter = screenWidth / 2.0;

      // حساب الموضع المطلوب للتوسيط المثالي 100%
      final perfectCenterPosition =
          itemCenterPosition - screenCenter + horizontalPadding;

      // التأكد من الحدود مع تفضيل التوسيط المثالي
      final maxScrollExtent = _daysScrollController.position.maxScrollExtent;
      final minScrollExtent = _daysScrollController.position.minScrollExtent;

      // استخدام الموضع المثالي حتى لو كان قريب من الحدود
      final finalPosition =
          perfectCenterPosition.clamp(minScrollExtent, maxScrollExtent);

      _daysScrollController.animateTo(
        finalPosition,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOutQuart, // منحنى أكثر سلاسة
      );
    }
  }

  // بناء شريط الأيام الأفقي
  Widget _buildDaysScrollBar() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        controller: _daysScrollController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        itemCount: _monthDays.length,
        itemBuilder: (context, index) {
          final date = _monthDays[index];
          final isSelected = _isSameDay(date, _selectedDate);
          final isToday = _isSameDay(date, DateTime.now());

          return GestureDetector(
            onTap: () => _selectDate(date),
            child: Container(
              width: 60,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: isSelected
                    ? const Color(0xFF00695C)
                    : isToday
                        ? const Color(0xFF00695C).withValues(alpha: 0.1)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF00695C)
                      : isToday
                          ? const Color(0xFF00695C)
                          : Colors.grey.shade300,
                  width: isSelected || isToday ? 2 : 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _getArabicDayName(date.weekday),
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: isSelected
                          ? Colors.white
                          : isToday
                              ? const Color(0xFF00695C)
                              : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${date.day}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? Colors.white
                          : isToday
                              ? const Color(0xFF00695C)
                              : Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // التحقق من تطابق التواريخ
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // اختيار تاريخ جديد
  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    _loadDebtsForSelectedDate();

    // التمرير إلى اليوم المحدد ليبقى في الوسط
    _scrollToSelectedDate(date);
  }

  // التمرير إلى تاريخ محدد
  void _scrollToSelectedDate(DateTime targetDate) {
    if (_monthDays.isEmpty) return;

    final targetIndex = _monthDays.indexWhere((date) =>
        date.day == targetDate.day &&
        date.month == targetDate.month &&
        date.year == targetDate.year);

    if (targetIndex != -1 && _daysScrollController.hasClients) {
      // نفس الحسابات الدقيقة المستخدمة في _scrollToCurrentDay
      const itemWidth = 60.0; // عرض كل عنصر يوم (متطابق مع التصميم)
      const horizontalPadding = 8.0; // الحشو الأفقي للقائمة
      const itemMargin = 8.0; // المسافة بين العناصر (4 من كل جانب = 8 إجمالي)

      // حساب الموضع الدقيق للعنصر المحدد
      final itemCenterPosition =
          (targetIndex * (itemWidth + itemMargin)) + (itemWidth / 2);

      // حساب عرض الشاشة الفعلي
      final screenWidth = MediaQuery.of(context).size.width;
      final screenCenter = screenWidth / 2.0;

      // حساب الموضع المطلوب للتوسيط المثالي 100%
      final perfectCenterPosition =
          itemCenterPosition - screenCenter + horizontalPadding;

      // التأكد من الحدود مع تفضيل التوسيط المثالي
      final maxScrollExtent = _daysScrollController.position.maxScrollExtent;
      final minScrollExtent = _daysScrollController.position.minScrollExtent;

      // استخدام الموضع المثالي للتوسيط الكامل
      final finalPosition =
          perfectCenterPosition.clamp(minScrollExtent, maxScrollExtent);

      _daysScrollController.animateTo(
        finalPosition,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOutCubic, // منحنى سلس للتنقل بين الأيام
      );
    }
  }

  // إعادة التمرير إلى التاريخ المناسب للتبويب (يمكن استدعاؤها من الخارج)
  void scrollToToday() {
    final now = DateTime.now();
    final targetDate = widget.isToday
        ? DateTime(now.year, now.month, now.day)
        : DateTime(now.year, now.month, now.day)
            .subtract(const Duration(days: 1));

    // تحديث التاريخ المحدد إلى التاريخ المناسب للتبويب
    setState(() {
      _selectedDate = targetDate;
    });

    // التمرير إلى التاريخ المحدد
    _scrollToCurrentDay();

    // تحميل ديون التاريخ المحدد
    _loadDebtsForSelectedDate();
  }

  // تحميل الديون للتاريخ المحدد
  void _loadDebtsForSelectedDate() {
    // سيتم تنفيذ هذا لاحقاً
    _loadDebts();
  }

  // الحصول على اسم اليوم بالعربية
  String _getArabicDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'الإثنين';
      case 2:
        return 'الثلاثاء';
      case 3:
        return 'الأربعاء';
      case 4:
        return 'الخميس';
      case 5:
        return 'الجمعة';
      case 6:
        return 'السبت';
      case 7:
        return 'الأحد';
      default:
        return '';
    }
  }

  Future<void> _loadDebts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب جميع الديون
      final allDebts = await _databaseHelper.getAllDebts();
      // استخدام التاريخ المحدد من شريط الأيام
      final targetDate = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
      );

      print('DEBUG: _loadDebts - التاريخ المستهدف: ${targetDate.toString()}');
      print('DEBUG: _loadDebts - widget.isToday: ${widget.isToday}');

      // فلترة الديون حسب التاريخ والاتجاه (المبيعات فقط - ديون العملاء لك)
      final filteredDebts = allDebts.where((debt) {
        final debtDay = DateTime(
          debt.entryDate.year,
          debt.entryDate.month,
          debt.entryDate.day,
        );
        final isMatch = debtDay.isAtSameMomentAs(targetDate) &&
            debt.direction == DebtDirection.customerOwesMe;

        if (isMatch) {
          print(
              'DEBUG: دين مطابق - تاريخ الدين: ${debtDay.toString()}, المبلغ: ${debt.amount}');
        }

        return isMatch;
      }).toList();

      print('DEBUG: عدد الديون المفلترة: ${filteredDebts.length}');

      // جلب معلومات العملاء
      final customerIds = filteredDebts.map((debt) => debt.customerId).toSet();
      final customers = <int, Customer>{};

      for (final customerId in customerIds) {
        final customer = await _databaseHelper.getCustomer(customerId);
        if (customer != null) {
          customers[customerId] = customer;
        }
      }

      setState(() {
        _debts = filteredDebts;
        _customers = customers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر الإحصائيات
          IconButton(
            onPressed: _toggleStatistics,
            icon: AnimatedRotation(
              turns: _isStatisticsExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                _isStatisticsExpanded
                    ? Icons.expand_less
                    : Icons.analytics_outlined,
                color: Colors.white,
              ),
            ),
            tooltip:
                _isStatisticsExpanded ? 'إخفاء الإحصائيات' : 'إظهار الإحصائيات',
          ),

          // زر نوع العرض
          PopupMenuButton<DailySalesViewType>(
            icon: Icon(
              _currentViewType == DailySalesViewType.compact
                  ? Icons.view_compact
                  : Icons.view_comfortable,
              size: 20,
              color: Colors.white,
            ),
            tooltip: 'نوع العرض',
            onSelected: (viewType) {
              setState(() {
                _currentViewType = viewType;
              });
            },
            itemBuilder: (context) => DailySalesViewType.values.map((viewType) {
              return PopupMenuItem<DailySalesViewType>(
                value: viewType,
                child: Row(
                  children: [
                    Icon(
                      viewType == DailySalesViewType.compact
                          ? Icons.view_compact
                          : Icons.view_comfortable,
                      size: 18,
                      color: _currentViewType == viewType
                          ? Colors.green.shade600
                          : Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      viewType.label,
                      style: TextStyle(
                        color: _currentViewType == viewType
                            ? Colors.green.shade600
                            : Colors.black87,
                        fontWeight: _currentViewType == viewType
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                    if (_currentViewType == viewType) ...[
                      const Spacer(),
                      Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.green.shade600,
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // شريط الأيام الأفقي
          _buildDaysScrollBar(),

          // قسم الإحصائيات القابل للطي
          SizeTransition(
            sizeFactor: _statisticsAnimation,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFF00695C).withValues(alpha: 0.05),
                border: Border(
                  bottom: BorderSide(
                    color: const Color(0xFF00695C).withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: _buildStatisticsContent(),
            ),
          ),

          // المحتوى الرئيسي
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _debts.isEmpty
                    ? _buildEmptyState()
                    : _currentViewType == DailySalesViewType.compact
                        ? _buildCompactView()
                        : _buildStandardView(),
          ),
        ],
      ),
    );
  }

  // تبديل حالة الإحصائيات
  void _toggleStatistics() {
    setState(() {
      _isStatisticsExpanded = !_isStatisticsExpanded;
      if (_isStatisticsExpanded) {
        _statisticsAnimationController.forward();
      } else {
        _statisticsAnimationController.reverse();
      }
    });
  }

  // بناء محتوى الإحصائيات
  Widget _buildStatisticsContent() {
    if (_debts.isEmpty) {
      return const SizedBox.shrink();
    }

    // حساب الإحصائيات
    final stats = _calculateStatistics();

    return SizedBox(
      height: 400, // ارتفاع ثابت للمنطقة القابلة للتمرير
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            // بطاقة إجمالي الكروت مع الرقم والمبلغ (العنصر الأول)
            _buildCenteredCardsWithAmountCard(
              title: 'إجمالي الكروت',
              cardsCount: stats['totalCards'],
              totalAmount: stats['totalAmount'],
              icon: Icons.credit_card,
            ),

            const SizedBox(height: 16),

            // بطاقة نسبة البيع مع النسبة في الوسط
            _buildCenteredSalesRatioCard(
              title: 'نسبة البيع',
              value: '${_calculateSalesRatio(stats).toStringAsFixed(1)}%',
              icon: Icons.trending_up,
            ),

            const SizedBox(height: 20),

            // أنواع الكروت مع المخطط
            _buildCardTypesSection(stats),

            const SizedBox(height: 20), // مساحة إضافية في النهاية
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            widget.isToday ? 'لا توجد ديون اليوم' : 'لا توجد ديون الأمس',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم إضافة أي ديون في هذا التاريخ',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  // العرض العادي
  Widget _buildStandardView() {
    return RefreshIndicator(
      onRefresh: _loadDebts,
      child: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _debts.length,
            itemBuilder: (context, index) {
              final debt = _debts[index];
              final customer = _customers[debt.customerId];

              if (customer == null) {
                return const SizedBox.shrink();
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: DebtCard(
                  debt: debt,
                  disableLongPress: true,
                  onDelete: () {
                    _loadDebts();
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  // العرض المضغوط
  Widget _buildCompactView() {
    return RefreshIndicator(
      onRefresh: _loadDebts,
      child: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _debts.length,
            itemBuilder: (context, index) {
              final debt = _debts[index];
              final customer = _customers[debt.customerId];

              if (customer == null) {
                return const SizedBox.shrink();
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: _buildCompactDebtCard(debt, customer),
              );
            },
          );
        },
      ),
    );
  }

  // بناء بطاقة دين مضغوطة (نفس تصميم نظرة عامة للديون)
  Widget _buildCompactDebtCard(Debt debt, Customer customer) {
    // التحقق من انتهاء الموعد وتاريخ أمس واليوم
    final isOverdue = debt.dueDate.isBefore(DateTime.now());
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final debtDate = DateTime(
      debt.entryDate.year,
      debt.entryDate.month,
      debt.entryDate.day,
    );
    final isToday = debtDate.isAtSameMomentAs(today);
    final isYesterday = debtDate.isAtSameMomentAs(yesterday);

    // تحديد لون البطاقة
    Color cardColor;
    Color borderColor;
    Color shadowColor;

    if (isOverdue) {
      cardColor = Colors.red.shade600;
      borderColor = Colors.red.shade700;
      shadowColor = Colors.red.withValues(alpha: 0.3);
    } else if (isYesterday) {
      cardColor = const Color(0xFF1A237E); // أزرق داكن مائل للأسود
      borderColor = const Color(0xFF0D1B69); // أزرق أكثر قتامة للحدود
      shadowColor = const Color(0xFF1A237E).withValues(alpha: 0.4);
    } else if (isToday) {
      cardColor = Colors.white;
      borderColor = Colors.green.shade300; // أخضر خفيف لبطاقات اليوم
      shadowColor = Colors.green.withValues(alpha: 0.1);
    } else {
      cardColor = Colors.white;
      borderColor = Colors.grey.shade200;
      shadowColor = Colors.grey.withValues(alpha: 0.05);
    }

    final isColored = isOverdue || isYesterday;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: borderColor,
        ),
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            blurRadius: isColored ? 4 : 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأول: اسم العميل والمبلغ
          Row(
            children: [
              // اسم العميل
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: isColored ? Colors.white : Colors.blue.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'العميل:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        customer.name,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: isColored ? Colors.white : Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // المبلغ
              Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    color: isColored ? Colors.white : Colors.green.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'المبلغ:',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${(debt.remainingAmount / 1000).toStringAsFixed(3)} ألف',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: isColored ? Colors.white : Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 6),

          const SizedBox(height: 6),

          // الصف الثاني: اسم الكارت والكمية
          Row(
            children: [
              // اسم الكارت
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.credit_card,
                      color: isColored ? Colors.white : Colors.orange.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'اسم الكارت:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _convertCardTypeToArabic(debt.cardType),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color:
                              isColored ? Colors.white : Colors.orange.shade700,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // الكمية
              Row(
                children: [
                  Icon(
                    Icons.inventory,
                    color: isColored ? Colors.white : Colors.purple.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'الكمية:',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${debt.quantity}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: isColored ? Colors.white : Colors.purple.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 6),

          // الصف الثالث: تاريخ القيد والوقت
          Row(
            children: [
              // تاريخ القيد
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: isColored ? Colors.white : Colors.blue.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'تاريخ القيد:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _formatFullDateWithDay(debt.entryDate),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color:
                              isColored ? Colors.white : Colors.blue.shade700,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // الوقت
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: isColored ? Colors.white : Colors.indigo.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'الوقت:',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTimeOnly(debt.entryDate),
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: isColored ? Colors.white : Colors.indigo.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 6),

          // الصف الرابع: تاريخ الاستحقاق
          Row(
            children: [
              Icon(
                Icons.event,
                color: isColored ? Colors.white : Colors.red.shade600,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                'تاريخ الاستحقاق:',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isColored
                      ? Colors.white.withValues(alpha: 0.8)
                      : Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  _formatFullDateWithDay(debt.dueDate),
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: isColored ? Colors.white : Colors.red.shade700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 6),

          // الصف الخامس: العدادات
          Row(
            children: [
              // عداد منذ القيد
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isColored
                        ? Colors.white.withValues(alpha: 0.2)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        color: isColored ? Colors.white : Colors.grey.shade600,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'منذ القيد: ${_getTimeSinceEntry(debt.entryDate)}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color:
                                isColored ? Colors.white : Colors.grey.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // عداد المتبقي للموعد
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isColored
                        ? Colors.white.withValues(alpha: 0.2)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getTimeUntilDue(debt.dueDate).contains('متأخر')
                            ? Icons.warning
                            : Icons.timer,
                        color: isColored
                            ? Colors.white
                            : (_getTimeUntilDue(debt.dueDate).contains('متأخر')
                                ? Colors.red.shade600
                                : Colors.grey.shade600),
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'للموعد: ${_getTimeUntilDue(debt.dueDate)}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: isColored
                                ? Colors.white
                                : (_getTimeUntilDue(debt.dueDate)
                                        .contains('متأخر')
                                    ? Colors.red.shade700
                                    : Colors.grey.shade700),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // التفاصيل (إذا وجدت)
          if (debt.notes?.isNotEmpty == true) ...[
            const SizedBox(height: 6),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.notes,
                  color: isColored ? Colors.white : Colors.indigo.shade600,
                  size: 14,
                ),
                const SizedBox(width: 6),
                Text(
                  'التفاصيل:',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: isColored
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    debt.notes!,
                    style: TextStyle(
                      fontSize: 11,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.9)
                          : Colors.indigo.shade700,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // حساب الإحصائيات المحسنة
  Map<String, dynamic> _calculateStatistics() {
    double totalAmount = 0;
    int totalCards = 0;
    int totalCustomers = 0;
    double averagePerCustomer = 0;
    double averagePerCard = 0;
    final Map<String, int> cardTypes = {};
    final Map<String, double> cardTypeAmounts = {};
    final Set<int> uniqueCustomers = {};

    for (final debt in _debts) {
      totalAmount += debt.remainingAmount;
      totalCards += debt.remainingQuantity;
      uniqueCustomers.add(debt.customerId);

      final cardType = _convertCardTypeToArabic(debt.cardType);
      cardTypes[cardType] = (cardTypes[cardType] ?? 0) + debt.remainingQuantity;
      cardTypeAmounts[cardType] =
          (cardTypeAmounts[cardType] ?? 0) + debt.remainingAmount;
    }

    totalCustomers = uniqueCustomers.length;
    averagePerCustomer = totalCustomers > 0 ? totalAmount / totalCustomers : 0;
    averagePerCard = totalCards > 0 ? totalAmount / totalCards : 0;

    return {
      'totalAmount': totalAmount,
      'totalCards': totalCards,
      'totalCustomers': totalCustomers,
      'averagePerCustomer': averagePerCustomer,
      'averagePerCard': averagePerCard,
      'cardTypes': cardTypes,
      'cardTypeAmounts': cardTypeAmounts,
    };
  }

  // تنسيق العملة مع فاصل الآلاف
  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }

  // بناء بطاقة إجمالي الكروت مع المبلغ
  Widget _buildCenteredCardsWithAmountCard({
    required String title,
    required int cardsCount,
    required double totalAmount,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان مع الأيقونة بجانبه من اليسار في الوسط
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة الصغيرة
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Icon(
                  icon,
                  color: Colors.blue,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              // العنوان
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // عدد الكروت
          Text(
            cardsCount.toString(),
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          // المبلغ تحت عدد الكروت
          Text(
            '${_formatCurrency(totalAmount)} ألف',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء بطاقة نسبة البيع مع أيقونة برتقالية
  Widget _buildCenteredSalesRatioCard({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان مع الأيقونة بجانبه من اليسار في الوسط
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة الصغيرة
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border:
                      Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Icon(
                  icon,
                  color: Colors.orange,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              // العنوان
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // القيمة في الوسط
          Text(
            value,
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم أنواع الكروت
  Widget _buildCardTypesSection(Map<String, dynamic> stats) {
    final cardTypes = stats['cardTypes'] as Map<String, int>;
    final totalCards = stats['totalCards'] as int;

    if (cardTypes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border:
            Border.all(color: const Color(0xFF00695C).withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00695C).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF00695C).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.pie_chart,
                  color: Color(0xFF00695C),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'أنواع الكروت المباعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF00695C),
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // قائمة أنواع الكروت (نفس تصميم الإحصائيات المتكاملة)
          ...cardTypes.entries.map((entry) {
            final cardType = entry.key;
            final quantity = entry.value;
            final percentage =
                totalCards > 0 ? (quantity / totalCards) * 100 : 0.0;
            // الحصول على المبلغ الحقيقي من البيانات
            final cardTypeAmounts =
                stats['cardTypeAmounts'] as Map<String, double>;
            final realAmount = cardTypeAmounts[cardType] ?? 0.0;
            final cardColor = _getCardTypeColorForAnalysis(cardType);

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [BoxShadow(color: cardColor.withValues(alpha: 0.1))],
                border: Border.all(color: cardColor.withValues(alpha: 0.2)),
              ),
              child: Column(
                children: [
                  // Header مع اسم البطاقة والنسبة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: cardColor.withValues(alpha: 0.05),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: cardColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.credit_card,
                              color: cardColor, size: 20),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            cardType,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: cardColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${percentage.toStringAsFixed(1)}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // المحتوى مع الإحصائيات
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // الكمية
                        Expanded(
                          child: _buildMiniStatItem(
                            'الكمية',
                            quantity.toString(),
                            Icons.inventory_2,
                            cardColor,
                          ),
                        ),
                        Container(
                            width: 1, height: 40, color: Colors.grey.shade200),
                        // العمليات
                        Expanded(
                          child: _buildMiniStatItem(
                            'العمليات',
                            '1', // يمكن حسابها من البيانات الفعلية
                            Icons.receipt,
                            Colors.blue,
                          ),
                        ),
                        Container(
                            width: 1, height: 40, color: Colors.grey.shade200),
                        // المبلغ
                        Expanded(
                          child: _buildMiniStatItem(
                            'المبلغ',
                            _formatCurrency(realAmount),
                            Icons.attach_money,
                            Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // شريط التقدم
                  Container(
                    margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    height: 6,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: percentage / 100,
                      child: Container(
                        decoration: BoxDecoration(
                          color: cardColor,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // الحصول على لون نوع الكارت للتحليل
  Color _getCardTypeColorForAnalysis(String cardType) {
    final colors = [
      const Color(0xFF4CAF50), // أخضر
      const Color(0xFF2196F3), // أزرق
      const Color(0xFF9C27B0), // بنفسجي
      const Color(0xFFFF9800), // برتقالي
      const Color(0xFFF44336), // أحمر
      const Color(0xFF607D8B), // رمادي مزرق
      const Color(0xFF795548), // بني
      const Color(0xFFE91E63), // وردي
    ];

    final index = cardType.hashCode % colors.length;
    return colors[index.abs()];
  }

  // حساب نسبة البيع الحقيقية
  double _calculateSalesRatio(Map<String, dynamic> stats) {
    final totalCards = stats['totalCards'] as int;
    final totalAmount = stats['totalAmount'] as double;

    // حساب نسبة البيع بناءً على متوسط سعر الكارت والهدف المتوقع
    // يمكن تخصيص هذا الحساب حسب منطق العمل
    if (totalCards == 0) return 0.0;

    final averageCardPrice = totalAmount / totalCards;
    const expectedPrice = 1000.0; // السعر المتوقع للكارت الواحد

    // حساب النسبة بناءً على الأداء مقارنة بالهدف
    final ratio = (averageCardPrice / expectedPrice) * 100;

    // تحديد النسبة بحد أقصى 100%
    return ratio > 100 ? 100.0 : ratio;
  }

  // عنصر إحصائية صغير (نفس تصميم الإحصائيات المتكاملة)
  Widget _buildMiniStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  // تحويل نوع الكارت إلى العربية
  String _convertCardTypeToArabic(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    // إذا كان النص عربي بالفعل، أعده كما هو
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(cardType)) {
      return cardType.trim();
    }

    // محاولة الحصول على الاسم من CardTypeProvider
    try {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );

      // البحث بالمعرف المباشر
      final cardTypeOption = cardTypeProvider.getCardTypeById(cardType);
      if (cardTypeOption != null) {
        return cardTypeOption.displayName;
      }

      // البحث في الأنواع المخصصة
      final customCardType = cardTypeProvider.customCardTypes
          .where((ct) => ct.name == cardType || ct.displayName == cardType)
          .firstOrNull;
      if (customCardType != null) {
        return customCardType.displayName;
      }
    } catch (e) {
      // في حالة الخطأ، نعيد الاسم الأصلي
    }

    // ترجمة احتياطية
    final lowerType = cardType.toLowerCase();
    if (lowerType.contains('zain')) return 'زين';
    if (lowerType.contains('asia')) return 'آسيا';
    if (lowerType.contains('cash')) return 'نقدي';

    return cardType;
  }

  // تنسيق التاريخ الكامل بالأرقام مع اسم اليوم
  String _formatFullDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];

    final dayName = dayNames[date.weekday % 7];
    final year = date.year.toString();
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');

    return '$dayName $year/$month/$day';
  }

  // تنسيق الوقت فقط
  String _formatTimeOnly(DateTime date) {
    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');

    if (hour == 0) {
      return '12:$minute ص';
    } else if (hour < 12) {
      return '$hour:$minute ص';
    } else if (hour == 12) {
      return '12:$minute م';
    } else {
      return '${hour - 12}:$minute م';
    }
  }

  // حساب الوقت منذ القيد
  String _getTimeSinceEntry(DateTime entryDate) {
    final now = DateTime.now();
    final difference = now.difference(entryDate);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'منذ 1 يوم';
      } else {
        return 'منذ ${difference.inDays} أيام';
      }
    } else if (difference.inHours > 0) {
      if (difference.inHours == 1) {
        return 'منذ 1 ساعة';
      } else {
        return 'منذ ${difference.inHours} ساعات';
      }
    } else if (difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return 'منذ 1 دقيقة';
      } else {
        return 'منذ ${difference.inMinutes} دقائق';
      }
    } else {
      return 'الآن';
    }
  }

  // حساب الوقت المتبقي للموعد
  String _getTimeUntilDue(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now);

    if (difference.isNegative) {
      final overdueDays = now.difference(dueDate).inDays;
      if (overdueDays == 0) {
        return 'متأخر اليوم';
      } else if (overdueDays == 1) {
        return 'متأخر منذ يوم';
      } else {
        return 'متأخر منذ $overdueDays أيام';
      }
    } else if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'يوم واحد';
      } else {
        return '${difference.inDays} أيام';
      }
    } else if (difference.inHours > 0) {
      if (difference.inHours == 1) {
        return 'ساعة واحدة';
      } else {
        return '${difference.inHours} ساعات';
      }
    } else if (difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return 'دقيقة واحدة';
      } else {
        return '${difference.inMinutes} دقائق';
      }
    } else {
      return 'الآن';
    }
  }
}
