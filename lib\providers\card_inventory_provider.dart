import 'package:flutter/foundation.dart';
import '../models/card_inventory.dart';
import '../database/database_helper.dart';
import 'form_data_provider.dart';

class CardInventoryProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<CardInventory> _inventories = [];
  bool _isLoading = false;

  List<CardInventory> get inventories => _inventories;
  bool get isLoading => _isLoading;

  // تحميل جميع الكميات
  Future<void> loadInventories() async {
    _isLoading = true;
    notifyListeners();

    try {
      _inventories = await _databaseHelper.getAllCardInventories();
      debugPrint('🔍 تم تحميل المخزون من قاعدة البيانات:');
      debugPrint('📦 عدد أنواع المخزون: ${_inventories.length}');

      // تحديث أسعار المخزون من أسعار أنواع البطاقات المحفوظة
      await _updateInventoryPricesFromCardTypes();

      for (final inventory in _inventories) {
        debugPrint('📋 نوع البطاقة: ${inventory.cardType}');
        debugPrint('🔢 الكمية: ${inventory.quantity}');
        debugPrint('💰 السعر: ${inventory.price}');
        debugPrint('💵 القيمة الإجمالية: ${inventory.totalValue}');
      }
    } catch (e) {
      debugPrint('Error loading inventories: $e');
      _inventories = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إضافة كمية جديدة
  Future<void> addInventory(CardInventory inventory) async {
    try {
      await _databaseHelper.insertCardInventory(inventory);
      await loadInventories();
    } catch (e) {
      debugPrint('Error adding inventory: $e');
      rethrow;
    }
  }

  // تحديث كمية
  Future<void> updateInventory(CardInventory inventory) async {
    try {
      await _databaseHelper.updateCardInventory(inventory);
      await loadInventories();
    } catch (e) {
      debugPrint('Error updating inventory: $e');
      rethrow;
    }
  }

  // حذف كمية
  Future<void> deleteInventory(int inventoryId) async {
    try {
      await _databaseHelper.deleteCardInventory(inventoryId);
      await loadInventories();
    } catch (e) {
      debugPrint('Error deleting inventory: $e');
      rethrow;
    }
  }

  // إضافة كمية لنوع بطاقة معين
  Future<bool> addStock(String cardType, int quantity,
      {double price = 0.0}) async {
    try {
      final existingInventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
        orElse: () => CardInventory(cardType: cardType, quantity: 0),
      );

      if (existingInventory.id != null) {
        // تحديث الكمية الموجودة
        final updatedInventory = existingInventory.copyWith(
          quantity: existingInventory.quantity + quantity,
          price: price > 0
              ? price
              : existingInventory.price, // تحديث السعر إذا تم توفيره
          updatedAt: DateTime.now(),
        );
        await updateInventory(updatedInventory);
      } else {
        // إضافة نوع بطاقة جديد
        final newInventory = CardInventory(
          cardType: cardType,
          quantity: quantity,
          price: price,
        );
        await addInventory(newInventory);
      }
      return true;
    } catch (e) {
      debugPrint('Error adding stock: $e');
      return false;
    }
  }

  // استقطاع كمية (عند البيع)
  Future<bool> deductStock(String cardType, int quantity) async {
    try {
      final existingInventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
        orElse: () => CardInventory(cardType: cardType, quantity: 0),
      );

      if (existingInventory.id == null) {
        // لا يوجد مخزون لهذا النوع
        debugPrint('⚠️ لا يوجد مخزون لنوع البطاقة: $cardType');
        return false;
      }

      if (existingInventory.quantity < quantity) {
        // الكمية المطلوبة أكبر من المتوفر
        debugPrint(
          '⚠️ الكمية المطلوبة ($quantity) أكبر من المتوفر (${existingInventory.quantity})',
        );
        return false;
      }

      // استقطاع الكمية
      final updatedInventory = existingInventory.copyWith(
        quantity: existingInventory.quantity - quantity,
        updatedAt: DateTime.now(),
      );

      await updateInventory(updatedInventory);
      debugPrint(
        '✅ تم استقطاع $quantity من $cardType. المتبقي: ${updatedInventory.quantity}',
      );
      return true;
    } catch (e) {
      debugPrint('Error deducting stock: $e');
      return false;
    }
  }

  // الحصول على كمية نوع بطاقة معين
  int getStockQuantity(String cardType) {
    try {
      final inventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
      );
      return inventory.quantity;
    } catch (e) {
      return 0;
    }
  }

  // الحصول على البطاقات منخفضة المخزون
  List<CardInventory> getLowStockItems() {
    return _inventories.where((inv) => inv.isLowStock).toList();
  }

  // الحصول على البطاقات النافدة
  List<CardInventory> getOutOfStockItems() {
    return _inventories.where((inv) => inv.isOutOfStock).toList();
  }

  // إجمالي قيمة المخزون (بالأسعار الفعلية)
  double getTotalInventoryValue() {
    double total = 0.0;
    for (final inventory in _inventories) {
      total += inventory.totalValue; // استخدام السعر الفعلي × الكمية
    }
    return total;
  }

  // إجمالي عدد البطاقات
  int getTotalCardsCount() {
    int total = 0;
    for (final inventory in _inventories) {
      total += inventory.quantity;
    }
    return total;
  }

  // الحصول على السعر الحقيقي لنوع كارت معين
  double getCardPrice(String cardType) {
    try {
      final inventory = _inventories.firstWhere(
        (inv) => inv.cardType == cardType,
      );
      return inventory.price;
    } catch (e) {
      return 0.0; // إذا لم يوجد، ارجع 0
    }
  }

  // تحديث أسعار المخزون من أسعار أنواع البطاقات المحفوظة
  Future<void> _updateInventoryPricesFromCardTypes() async {
    try {
      // تحميل أسعار أنواع البطاقات من FormDataProvider
      final formDataProvider = FormDataProvider();
      await formDataProvider.loadFormData();
      final cardTypePrices = formDataProvider.cardTypePrices;

      debugPrint('🔄 تحديث أسعار المخزون من أسعار أنواع البطاقات:');
      debugPrint('💰 أسعار أنواع البطاقات المحفوظة: $cardTypePrices');

      bool hasUpdates = false;

      for (int i = 0; i < _inventories.length; i++) {
        final inventory = _inventories[i];
        final savedPrice = cardTypePrices[inventory.cardType];

        if (savedPrice != null &&
            savedPrice > 0 &&
            inventory.price != savedPrice) {
          debugPrint(
              '🔧 تحديث سعر ${inventory.cardType} من ${inventory.price} إلى $savedPrice');

          // إنشاء نسخة محدثة من المخزون
          final updatedInventory = CardInventory(
            id: inventory.id,
            cardType: inventory.cardType,
            quantity: inventory.quantity,
            price: savedPrice,
            createdAt: inventory.createdAt,
            updatedAt: DateTime.now(),
          );

          // تحديث في قاعدة البيانات
          await _databaseHelper.updateCardInventory(updatedInventory);

          // تحديث في القائمة المحلية
          _inventories[i] = updatedInventory;
          hasUpdates = true;
        }
      }

      if (hasUpdates) {
        debugPrint('✅ تم تحديث أسعار المخزون بنجاح');
        notifyListeners();
      } else {
        debugPrint('ℹ️ لا توجد أسعار تحتاج إلى تحديث');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث أسعار المخزون: $e');
    }
  }
}
