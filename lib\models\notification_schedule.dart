import 'package:flutter/material.dart';

// نموذج جدولة التنبيهات
class NotificationSchedule {
  NotificationSchedule({
    required this.id,
    required this.name,
    required this.time,
    required this.weekdays,
    required this.isEnabled,
    required this.type,
    this.customMessage,
  });

  // إنشاء من JSON
  factory NotificationSchedule.fromJson(Map<String, dynamic> json) {
    final timeParts = json['time'].split(':');
    return NotificationSchedule(
      id: json['id'],
      name: json['name'],
      time: TimeOfDay(
        hour: int.parse(timeParts[0]),
        minute: int.parse(timeParts[1]),
      ),
      weekdays: List<int>.from(json['weekdays']),
      isEnabled: json['isEnabled'],
      type: NotificationScheduleType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationScheduleType.daily,
      ),
      customMessage: json['customMessage'],
    );
  }
  final int id;
  final String name;
  final TimeOfDay time;
  final List<int> weekdays; // 1-7 (الاثنين-الأحد)
  final bool isEnabled;
  final NotificationScheduleType type;
  final String? customMessage;

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'time': '${time.hour}:${time.minute}',
      'weekdays': weekdays,
      'isEnabled': isEnabled,
      'type': type.name,
      'customMessage': customMessage,
    };
  }

  // نسخ مع تعديل
  NotificationSchedule copyWith({
    int? id,
    String? name,
    TimeOfDay? time,
    List<int>? weekdays,
    bool? isEnabled,
    NotificationScheduleType? type,
    String? customMessage,
  }) {
    return NotificationSchedule(
      id: id ?? this.id,
      name: name ?? this.name,
      time: time ?? this.time,
      weekdays: weekdays ?? this.weekdays,
      isEnabled: isEnabled ?? this.isEnabled,
      type: type ?? this.type,
      customMessage: customMessage ?? this.customMessage,
    );
  }

  // تحويل الوقت إلى نص عربي
  String get formattedTime {
    final hour = time.hour;
    final minute = time.minute;
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'ظهراً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  // تحويل أيام الأسبوع إلى نص عربي
  String get formattedWeekdays {
    if (weekdays.length == 7) return 'يومياً';
    if (weekdays.isEmpty) return 'لا يوجد';

    final dayNames = {
      1: 'الاثنين',
      2: 'الثلاثاء',
      3: 'الأربعاء',
      4: 'الخميس',
      5: 'الجمعة',
      6: 'السبت',
      7: 'الأحد',
    };

    final selectedDays = weekdays.map((day) => dayNames[day]!).toList();

    if (selectedDays.length <= 3) {
      return selectedDays.join(', ');
    } else {
      return '${selectedDays.take(2).join(', ')} و ${selectedDays.length - 2} أيام أخرى';
    }
  }
}

// أنواع جدولة التنبيهات
enum NotificationScheduleType {
  daily('تنبيه يومي'),
  overdue('تنبيه الديون المتأخرة'),
  dueToday('تنبيه الديون المستحقة اليوم'),
  dueSoon('تنبيه الديون المستحقة قريباً'),
  summary('ملخص يومي'),
  custom('تنبيه مخصص');

  const NotificationScheduleType(this.displayName);
  final String displayName;
}

// إعدادات التنبيهات المتقدمة
class AdvancedNotificationSettings {
  AdvancedNotificationSettings({
    this.backgroundNotificationsEnabled = true,
    this.persistentNotifications = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.snoozeMinutes = 10,
    this.schedules = const [],
  });

  // إنشاء من JSON
  factory AdvancedNotificationSettings.fromJson(Map<String, dynamic> json) {
    return AdvancedNotificationSettings(
      backgroundNotificationsEnabled:
          json['backgroundNotificationsEnabled'] ?? true,
      persistentNotifications: json['persistentNotifications'] ?? true,
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      snoozeMinutes: json['snoozeMinutes'] ?? 10,
      schedules: (json['schedules'] as List<dynamic>?)
              ?.map((s) => NotificationSchedule.fromJson(s))
              .toList() ??
          [],
    );
  }
  final bool backgroundNotificationsEnabled;
  final bool persistentNotifications;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final int snoozeMinutes;
  final List<NotificationSchedule> schedules;

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'backgroundNotificationsEnabled': backgroundNotificationsEnabled,
      'persistentNotifications': persistentNotifications,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'snoozeMinutes': snoozeMinutes,
      'schedules': schedules.map((s) => s.toJson()).toList(),
    };
  }

  // نسخ مع تعديل
  AdvancedNotificationSettings copyWith({
    bool? backgroundNotificationsEnabled,
    bool? persistentNotifications,
    bool? soundEnabled,
    bool? vibrationEnabled,
    int? snoozeMinutes,
    List<NotificationSchedule>? schedules,
  }) {
    return AdvancedNotificationSettings(
      backgroundNotificationsEnabled:
          backgroundNotificationsEnabled ?? this.backgroundNotificationsEnabled,
      persistentNotifications:
          persistentNotifications ?? this.persistentNotifications,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      snoozeMinutes: snoozeMinutes ?? this.snoozeMinutes,
      schedules: schedules ?? this.schedules,
    );
  }
}
