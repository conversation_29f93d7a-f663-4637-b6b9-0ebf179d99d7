import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:permission_handler/permission_handler.dart';
import 'providers/customer_provider.dart';
import 'providers/debt_provider.dart';
import 'providers/customer_statistics_provider.dart';
import 'providers/card_type_provider.dart';
import 'providers/card_profit_provider.dart';
import 'providers/card_inventory_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/smart_notification_provider.dart';
import 'providers/form_data_provider.dart';
import 'providers/font_provider.dart';
import 'providers/theme_provider.dart';
import 'screens/splash_screen.dart';

// طلب الصلاحيات المطلوبة
Future<void> _requestPermissions() async {
  try {
    // طلب صلاحيات الصوت والاهتزاز
    await [
      Permission.audio,
      Permission.notification,
    ].request();

    debugPrint('✅ تم طلب صلاحيات الصوت والإشعارات');
  } catch (e) {
    debugPrint('⚠️ خطأ في طلب الصلاحيات: $e');
  }
}

void main() async {
  // Force rebuild - Version 4 with UI improvements
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تهيئة بسيطة بدون خدمات معقدة
    debugPrint('🚀 بدء تشغيل التطبيق...');

    // Initialize Arabic locale for date formatting
    await initializeDateFormatting('ar');
    debugPrint('✅ تم تهيئة اللغة العربية');

    // طلب الصلاحيات المطلوبة (بدون انتظار لتجنب التأخير)
    _requestPermissions().catchError((e) {
      debugPrint('⚠️ خطأ في طلب الصلاحيات: $e');
    });

    debugPrint('✅ تم تهيئة التطبيق بنجاح');
    runApp(const MyApp());
  } catch (e, stackTrace) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    debugPrint('Stack trace: $stackTrace');

    // تشغيل التطبيق مع معالجة الأخطاء
    runApp(ErrorApp(error: e.toString()));
  }
}

// تطبيق معالجة الأخطاء
class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key, required this.error});
  final String error;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'خطأ في التطبيق',
      home: Scaffold(
        backgroundColor: Colors.red[50],
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 80, color: Colors.red[600]),
                const SizedBox(height: 20),
                Text(
                  'حدث خطأ في تشغيل التطبيق',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[800],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                Text(
                  'يرجى إعادة تشغيل التطبيق',
                  style: TextStyle(fontSize: 16, color: Colors.red[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Text(
                    'تفاصيل الخطأ:\n$error',
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Customer Provider بدون تحميل فوري
        ChangeNotifierProvider(create: (_) => CustomerProvider()),

        // Debt Provider بدون تحميل فوري
        ChangeNotifierProvider(create: (_) => DebtProvider()),

        // Customer Statistics Provider مبسط
        ChangeNotifierProxyProvider<DebtProvider, CustomerStatisticsProvider>(
          create: (context) => CustomerStatisticsProvider(
            Provider.of<DebtProvider>(context, listen: false),
          ),
          update: (context, debtProvider, previous) =>
              previous ?? CustomerStatisticsProvider(debtProvider),
        ),

        // باقي Providers بدون تحميل فوري
        ChangeNotifierProvider(create: (_) => CardTypeProvider()),
        ChangeNotifierProvider(create: (_) => CardProfitProvider()),
        ChangeNotifierProvider(create: (_) => CardInventoryProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => SmartNotificationProvider()),
        ChangeNotifierProvider(create: (_) => FormDataProvider()),
        ChangeNotifierProvider(create: (_) => FontProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: MaterialApp(
        title: 'محاسب ديون احترافي',
        debugShowCheckedModeBanner: false,
        locale: const Locale('ar', 'SA'),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('ar', 'SA'), Locale('en', 'US')],
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Cairo',
          textTheme: const TextTheme(
            bodyLarge: TextStyle(fontSize: 16),
            bodyMedium: TextStyle(fontSize: 14),
          ),
        ),
        home: const SplashScreen(),
      ),
    );
  }
}
