import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// مُحسِّن نموذج إضافة الدين
/// يحتوي على تحسينات الأداء والاستجابة السريعة
class DebtFormOptimizer {
  static const Duration _debounceDelay = Duration(milliseconds: 300);
  static const Duration _fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration _normalAnimationDuration = Duration(milliseconds: 200);

  /// تحسين أداء البحث مع Debouncing
  static Timer? _searchTimer;

  static void debouncedSearch({
    required String query,
    required VoidCallback onSearch,
  }) {
    _searchTimer?.cancel();
    _searchTimer = Timer(_debounceDelay, () {
      onSearch();
    });
  }

  /// تنظيف مؤقتات البحث
  static void disposeSearchTimer() {
    _searchTimer?.cancel();
    _searchTimer = null;
  }

  /// تحسين الرسوم المتحركة للسرعة
  static AnimationController createFastAnimationController({
    required TickerProvider vsync,
    bool isLongAnimation = false,
  }) {
    return AnimationController(
      duration:
          isLongAnimation ? _normalAnimationDuration : _fastAnimationDuration,
      vsync: vsync,
    );
  }

  /// تحسين أداء القوائم الطويلة
  static Widget buildOptimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      controller: controller,
      padding: padding,
      // تحسينات الأداء
      cacheExtent: 500.0, // تقليل cache للسرعة
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      addAutomaticKeepAlives: false, // تحسين الذاكرة
      addSemanticIndexes: false, // تقليل العمليات
    );
  }

  /// تحسين أداء الحقول النصية
  static InputDecoration buildOptimizedInputDecoration({
    required String labelText,
    IconData? prefixIcon,
    Color? iconColor,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      prefixIcon: prefixIcon != null
          ? Icon(prefixIcon, color: iconColor, size: 22)
          : null,
      suffixIcon: suffixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey.shade500, width: 1.5),
      ),
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 12,
      ),
    );
  }

  /// تحسين أداء الأزرار
  static Widget buildOptimizedButton({
    required VoidCallback onPressed,
    required Widget child,
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
    bool isLoading = false,
  }) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding: padding ??
            const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
            ),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        elevation: 2,
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : child,
    );
  }

  /// تحسين أداء البطاقات
  static Widget buildOptimizedCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? color,
    double? elevation,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color ?? Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: elevation ?? 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  /// تحسين التفاعل مع اللمس
  static void optimizedHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  /// تحسين أداء التحقق من صحة البيانات
  static String? optimizedValidator({
    required String? value,
    required String fieldName,
    bool isRequired = true,
    int? minLength,
    int? maxLength,
    String? pattern,
  }) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'يرجى إدخال $fieldName';
    }

    if (value != null && value.isNotEmpty) {
      if (minLength != null && value.length < minLength) {
        return '$fieldName يجب أن يكون $minLength أحرف على الأقل';
      }

      if (maxLength != null && value.length > maxLength) {
        return '$fieldName يجب أن يكون $maxLength أحرف كحد أقصى';
      }

      if (pattern != null && !RegExp(pattern).hasMatch(value)) {
        return '$fieldName غير صحيح';
      }
    }

    return null;
  }

  /// تحسين أداء تنسيق الأرقام
  static String formatNumber(int number) {
    if (number < 1000) return number.toString();
    if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(number % 1000 == 0 ? 0 : 1)}ألف';
    }
    return '${(number / 1000000).toStringAsFixed(number % 1000000 == 0 ? 0 : 1)}م';
  }

  /// تحسين أداء تنسيق العملة
  static String formatCurrency(double amount) {
    final intAmount = amount.toInt();
    return 'د ع ${formatNumber(intAmount)}';
  }

  /// تحسين أداء البحث في القوائم
  static List<T> searchInList<T>({
    required List<T> items,
    required String query,
    required String Function(T) getSearchText,
    int maxResults = 50,
  }) {
    if (query.isEmpty) return items.take(maxResults).toList();

    final lowerQuery = query.toLowerCase().trim();
    final results = <T>[];

    // البحث السريع أولاً
    for (final item in items) {
      if (results.length >= maxResults) break;

      final text = getSearchText(item).toLowerCase();
      if (text.contains(lowerQuery)) {
        results.add(item);
      }
    }

    // ترتيب النتائج حسب الصلة
    results.sort((a, b) {
      final aText = getSearchText(a).toLowerCase();
      final bText = getSearchText(b).toLowerCase();

      final aStarts = aText.startsWith(lowerQuery);
      final bStarts = bText.startsWith(lowerQuery);

      if (aStarts && !bStarts) return -1;
      if (!aStarts && bStarts) return 1;

      return aText.compareTo(bText);
    });

    return results;
  }

  /// تحسين أداء التخزين المؤقت
  static final Map<String, dynamic> _cache = {};

  static T? getCached<T>(String key) {
    return _cache[key] as T?;
  }

  static void setCached<T>(String key, T value) {
    _cache[key] = value;
  }

  static void clearCache() {
    _cache.clear();
  }

  /// تنظيف جميع الموارد
  static void dispose() {
    disposeSearchTimer();
    clearCache();
  }
}
