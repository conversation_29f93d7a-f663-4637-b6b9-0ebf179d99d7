// ═══════════════════════════════════════════════════════════════════════════════
// نظرة عامة للديون (Debts Overview)
// ═══════════════════════════════════════════════════════════════════════════════
// هذا الملف يحتوي على نظرة عامة شاملة لديون جميع العملاء
// وهو مختلف تماماً عن "قائمة الديون" (debts_tab.dart)
//
// الفرق:
// - نظرة عامة للديون: تعرض ديون جميع العملاء مجمعة في جداول منفصلة
// - قائمة الديون: تعرض ديون عميل واحد محدد فقط
// ═══════════════════════════════════════════════════════════════════════════════

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../providers/customer_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_statistics_provider.dart';
import '../widgets/debt_card.dart';

class DebtsOverviewScreen extends StatefulWidget {
  const DebtsOverviewScreen({super.key});

  @override
  State<DebtsOverviewScreen> createState() => _DebtsOverviewScreenState();
}

class _DebtsOverviewScreenState extends State<DebtsOverviewScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Map<Customer, List<Debt>> _customerDebtsMap = {};
  List<Customer> _filteredCustomers = [];
  bool _isLoading = true;

  // متغيرات التحديد المتعدد
  bool _isMultiSelectMode = false;
  final Set<int> _selectedDebtIds = {};
  final String _multiSelectMode = 'payment'; // 'payment' أو 'delete'

  // متغير التصنيف الزمني المحدد
  String _selectedTimeCategory = 'الكل';

  @override
  void initState() {
    super.initState();
    _loadData();

    // الاستماع لتغييرات DebtProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.addListener(_onDebtProviderChanged);
    });
  }

  // دالة للاستجابة لتغييرات DebtProvider
  void _onDebtProviderChanged() {
    if (mounted) {
      // تأخير التحديث قليلاً لتجنب مشاكل التمرير
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _updateCustomerDebtsMap();
        }
      });
    }
  }

  // دالة تحديث ذكية للديون
  void _updateCustomerDebtsMap() {
    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);

      // تجميع الديون حسب العملاء
      final Map<Customer, List<Debt>> customerDebtsMap = {};

      for (final customer in customerProvider.customers) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == customer.id)
            .toList();

        if (customerDebts.isNotEmpty) {
          customerDebtsMap[customer] = customerDebts;
        }
      }

      // تحقق من وجود تغييرات قبل إعادة البناء
      if (mounted) {
        final newFilteredCustomers = customerDebtsMap.keys.toList();
        newFilteredCustomers.sort((a, b) => a.name.compareTo(b.name));

        // تحديث فقط إذا كان هناك تغيير فعلي
        bool hasChanges = false;
        if (_customerDebtsMap.length != customerDebtsMap.length ||
            _filteredCustomers.length != newFilteredCustomers.length) {
          hasChanges = true;
        } else {
          // تحقق من تغيير في محتوى الديون
          for (final customer in newFilteredCustomers) {
            final oldDebts = _customerDebtsMap[customer] ?? [];
            final newDebts = customerDebtsMap[customer] ?? [];
            if (oldDebts.length != newDebts.length) {
              hasChanges = true;
              break;
            }
          }
        }

        if (hasChanges) {
          setState(() {
            _customerDebtsMap = customerDebtsMap;
            _filteredCustomers = newFilteredCustomers;
          });
        }
      }
    } catch (e) {
      // تجاهل الأخطاء للحفاظ على الاستقرار
      debugPrint('Error updating customer debts map: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();

    // إزالة المستمع
    try {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.removeListener(_onDebtProviderChanged);
    } catch (e) {
      // تجاهل الأخطاء عند التخلص من الموارد
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final statisticsProvider = Provider.of<CustomerStatisticsProvider>(
        context,
        listen: false,
      );

      // تحميل العملاء والديون والمدفوعات
      await Future.wait([
        customerProvider.loadCustomers(),
        debtProvider.loadAllDebts(),
        debtProvider.loadAllPayments(),
      ]);

      // تحميل الإحصائيات
      await statisticsProvider.calculateStatistics(customerProvider.customers);

      // تجميع الديون حسب العملاء
      final Map<Customer, List<Debt>> customerDebtsMap = {};

      for (final customer in customerProvider.customers) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == customer.id)
            .toList();

        if (customerDebts.isNotEmpty) {
          customerDebtsMap[customer] = customerDebts;
        }
      }

      setState(() {
        _customerDebtsMap = customerDebtsMap;
        _filteredCustomers = customerDebtsMap.keys.toList();
        // ترتيب العملاء أبجدياً حسب الاسم
        _filteredCustomers.sort((a, b) => a.name.compareTo(b.name));
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterCustomers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCustomers = _customerDebtsMap.keys.toList();
      } else {
        _filteredCustomers = _customerDebtsMap.keys.where((customer) {
          return customer.name.toLowerCase().contains(query.toLowerCase()) ||
              (customer.phone?.contains(query) ?? false);
        }).toList();
      }
      // ترتيب العملاء أبجدياً حسب الاسم
      _filteredCustomers.sort((a, b) => a.name.compareTo(b.name));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'نظرة عامة للديون',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Stack(
        children: [
          // المحتوى الرئيسي
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredCustomers.isEmpty
                  ? Column(
                      children: [
                        _buildSearchBar(),
                        Expanded(child: _buildEmptyState()),
                      ],
                    )
                  : Column(
                      children: [
                        // الشريط العلوي لإحصائيات التحديد
                        if (_isMultiSelectMode) _buildSelectionTopBar(),

                        // المحتوى الرئيسي
                        Expanded(
                          child: RefreshIndicator(
                            onRefresh: _loadData,
                            child: CustomScrollView(
                              controller: _scrollController,
                              physics: const AlwaysScrollableScrollPhysics(),
                              slivers: [
                                // شريط البحث
                                SliverToBoxAdapter(
                                  child: _buildSearchBar(),
                                ),

                                // بطاقات التصنيف الزمني
                                SliverToBoxAdapter(
                                  child: _buildTimeClassificationCards(),
                                ),

                                // شريط أدوات التحديد المتعدد
                                if (_isMultiSelectMode)
                                  SliverToBoxAdapter(
                                    child: _buildMultiSelectToolbar(),
                                  ),

                                // جداول منفصلة لكل عميل
                                SliverList(
                                  delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                                      final customer =
                                          _filteredCustomers[index];
                                      final customerDebts =
                                          _customerDebtsMap[customer] ?? [];

                                      // فلترة ديون العميل حسب التصنيف المحدد
                                      final filteredCustomerDebts =
                                          _getFilteredDebtsByCategory(
                                              customerDebts,
                                              _selectedTimeCategory);

                                      // فلترة الديون النشطة فقط (التي لها مبلغ متبقي)
                                      final activeCustomerDebts =
                                          filteredCustomerDebts
                                              .where((debt) =>
                                                  debt.remainingAmount > 0)
                                              .toList();

                                      if (activeCustomerDebts.isEmpty) {
                                        return const SizedBox.shrink();
                                      }

                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0, vertical: 4.0),
                                        child: _buildCustomerTable(customer,
                                            activeCustomerDebts, customerDebts),
                                      );
                                    },
                                    childCount: _filteredCustomers.length,
                                  ),
                                ),

                                // مساحة إضافية في الأسفل
                                const SliverToBoxAdapter(
                                  child: SizedBox(
                                      height: 80), // مساحة إضافية للشريط السفلي
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

          // الشريط السفلي لأدوات التحديد
          if (_isMultiSelectMode) _buildSelectionBottomBar(),
        ],
      ),
    );
  }

  // بناء جدول منفصل لعميل واحد
  Widget _buildCustomerTable(Customer customer, List<Debt> debts,
      [List<Debt>? allCustomerDebts]) {
    if (debts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // رأس الجدول مع اسم العميل
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, Colors.teal.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                // اسم العميل
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: Colors.teal.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      customer.name,
                      style: TextStyle(
                        color: Colors.teal.shade800,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${debts.where((debt) => debt.remainingAmount > 0).length} عملية',
                      style: TextStyle(
                        color: Colors.teal.shade600,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // العدادات المصغرة للعميل
                _buildTableMiniCounters(debts,
                    allCustomerDebts: allCustomerDebts),
              ],
            ),
          ),

          // محتوى الجدول
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 4.5,
              child: DataTable(
                columnSpacing: 2,
                horizontalMargin: 4,
                checkboxHorizontalMargin: 4,
                headingRowHeight: 28,
                dataRowMinHeight: 40,
                dataRowMaxHeight: 40,
                headingRowColor: WidgetStateProperty.all(Colors.teal.shade600),
                border: TableBorder.all(
                  color: Colors.grey.shade400,
                ),
                dividerThickness: 1,
                columns: _buildTableColumns(debts),
                rows: _buildTableRows(debts),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء أعمدة الجدول
  List<DataColumn> _buildTableColumns(List<Debt> debts) {
    // التحقق من وجود ديون بمبالغ متبقية (دفع جزئي)
    final hasPartialPayments = debts.any((debt) =>
        debt.remainingAmount > 0 && debt.remainingAmount < debt.amount);

    return [
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'العميل',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'نوع الكارت',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الكمية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المبلغ الكلي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      if (hasPartialPayments)
        const DataColumn(
          label: Expanded(
            child: Center(
              child: Text(
                'المدفوع',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  fontFamily: 'Calibri',
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      if (hasPartialPayments)
        const DataColumn(
          label: Expanded(
            child: Center(
              child: Text(
                'المتبقي',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  fontFamily: 'Calibri',
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      if (hasPartialPayments)
        const DataColumn(
          label: Expanded(
            child: Center(
              child: Text(
                'الكارتات المتبقية',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  fontFamily: 'Calibri',
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'تاريخ الدخول',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'تاريخ الاستحقاق',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الملاحظات',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'منذ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الأيام المتبقية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    ];
  }

  // ترتيب الديون حسب الأولوية
  List<Debt> _sortDebtsByPriority(List<Debt> debts) {
    final sortedDebts = List<Debt>.from(debts);

    sortedDebts.sort((a, b) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final thisWeekStart = today.subtract(Duration(days: today.weekday - 1));
      final thisMonthStart = DateTime(today.year, today.month);

      final aEntryDate =
          DateTime(a.entryDate.year, a.entryDate.month, a.entryDate.day);
      final bEntryDate =
          DateTime(b.entryDate.year, b.entryDate.month, b.entryDate.day);

      // تحديد أولوية كل دين
      int getPriority(Debt debt, DateTime entryDate) {
        final isOverdue = debt.dueDate.isBefore(now);
        final isToday = entryDate.isAtSameMomentAs(today);
        final isYesterday = entryDate.isAtSameMomentAs(yesterday);
        final isThisWeek = entryDate
                .isAfter(thisWeekStart.subtract(const Duration(days: 1))) &&
            entryDate.isBefore(today);
        final isThisMonth = entryDate
                .isAfter(thisMonthStart.subtract(const Duration(days: 1))) &&
            entryDate.isBefore(thisWeekStart);

        if (isToday) return 1; // اليوم - أولوية عليا
        if (isYesterday) return 2; // أمس
        if (isThisWeek) return 3; // هذا الأسبوع
        if (isThisMonth) return 4; // هذا الشهر
        if (isOverdue) return 5; // المتأخرة - أولوية منخفضة
        return 6; // باقي الديون
      }

      final aPriority = getPriority(a, aEntryDate);
      final bPriority = getPriority(b, bEntryDate);

      // ترتيب حسب الأولوية، ثم حسب التاريخ
      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }

      // إذا كانت نفس الأولوية، رتب حسب التاريخ (الأحدث أولاً)
      return b.entryDate.compareTo(a.entryDate);
    });

    return sortedDebts;
  }

  // بناء صفوف الجدول
  List<DataRow> _buildTableRows(List<Debt> debts) {
    // التحقق من وجود ديون بمبالغ متبقية (دفع جزئي)
    final hasPartialPayments = debts.any((debt) =>
        debt.remainingAmount > 0 && debt.remainingAmount < debt.amount);

    // ترتيب الديون حسب الأولوية
    final sortedDebts = _sortDebtsByPriority(debts);

    return sortedDebts.map((debt) {
      // العثور على العميل المرتبط بهذا الدين
      final customer = _filteredCustomers.firstWhere(
        (c) => c.id == debt.customerId,
        orElse: () => Customer(
            id: 0,
            name: 'غير معروف',
            phone: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now()),
      );

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final thisWeekStart = today.subtract(Duration(days: today.weekday - 1));
      final thisMonthStart = DateTime(today.year, today.month);

      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);

      final isOverdue = debt.dueDate.isBefore(now);
      final isToday = debtEntryDate.isAtSameMomentAs(today);
      final isYesterday = debtEntryDate.isAtSameMomentAs(yesterday);
      final isThisWeek = debtEntryDate
              .isAfter(thisWeekStart.subtract(const Duration(days: 1))) &&
          debtEntryDate.isBefore(today);
      final isThisMonth = debtEntryDate
              .isAfter(thisMonthStart.subtract(const Duration(days: 1))) &&
          debtEntryDate.isBefore(thisWeekStart);

      Color? rowColor;
      Color? textColor;
      if (isOverdue) {
        rowColor = Colors.red.shade600;
        textColor = Colors.white;
      } else if (isToday) {
        rowColor = Colors.green.shade600;
        textColor = Colors.white;
      } else if (isYesterday) {
        rowColor = const Color(0xFF1A237E);
        textColor = Colors.white;
      } else if (isThisWeek) {
        rowColor = Colors.blue.shade100;
        textColor = null;
      } else if (isThisMonth) {
        rowColor = Colors.orange.shade100;
        textColor = null;
      }

      final cells = <DataCell>[
        // العميل
        DataCell(
          Center(
            child: Text(
              customer.name,
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // نوع الكارت
        DataCell(
          Center(
            child: Text(
              _convertCardTypeToArabic(debt.cardType),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // الكمية
        DataCell(
          Center(
            child: Text(
              debt.quantity.toString(),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // المبلغ الكلي
        DataCell(
          Center(
            child: Text(
              _formatCurrencyWithThousands(debt.amount),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ];

      // إضافة أعمدة الدفع الجزئي إذا وجدت
      if (hasPartialPayments) {
        final paidAmount = debt.amount - debt.remainingAmount;
        cells.addAll([
          // المدفوع
          DataCell(
            Center(
              child: Text(
                paidAmount > 0 ? _formatCurrencyWithThousands(paidAmount) : '-',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Calibri',
                  color: textColor ??
                      (paidAmount > 0 ? Colors.green.shade700 : Colors.grey),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // المتبقي
          DataCell(
            Center(
              child: Text(
                debt.remainingAmount > 0
                    ? _formatCurrencyWithThousands(debt.remainingAmount)
                    : '-',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Calibri',
                  color: textColor ??
                      (debt.remainingAmount > 0
                          ? Colors.red.shade700
                          : Colors.grey),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // الكارتات المتبقية
          DataCell(
            Center(
              child: Text(
                _hasPartialPayment(debt)
                    ? _calculateRemainingCards(debt).toString()
                    : '-',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Calibri',
                  color: textColor ??
                      (_hasPartialPayment(debt)
                          ? Colors.orange.shade700
                          : Colors.grey),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ]);
      }

      cells.addAll([
        // تاريخ الدخول
        DataCell(
          Center(
            child: Text(
              _formatDateWithDayName(debt.entryDate, includeTime: true),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        // تاريخ الاستحقاق
        DataCell(
          Center(
            child: Text(
              _formatDateWithDayName(debt.dueDate),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor ?? (isOverdue ? Colors.red.shade700 : null),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        // الملاحظات
        DataCell(
          Center(
            child: Text(
              debt.notes ?? '-',
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        // منذ القيد
        DataCell(
          Center(
            child: Text(
              _getDaysSinceEntry(debt.entryDate),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // الأيام المتبقية
        DataCell(
          Center(
            child: Text(
              _getDaysRemaining(debt.dueDate),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                fontFamily: 'Calibri',
                color: textColor ??
                    (isOverdue ? Colors.red.shade700 : Colors.green.shade700),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ]);

      return DataRow(
        color: WidgetStateProperty.all(rowColor),
        selected: _selectedDebtIds.contains(debt.id),
        onSelectChanged: (selected) {
          // التعامل مع مربع الصح فقط
          if (selected == true) {
            setState(() {
              _selectedDebtIds.add(debt.id!);
              if (!_isMultiSelectMode) {
                _isMultiSelectMode = true;
              }
            });
          } else {
            setState(() {
              _selectedDebtIds.remove(debt.id);
              if (_selectedDebtIds.isEmpty) {
                _isMultiSelectMode = false;
              }
            });
          }
        },
        cells: cells.map((cell) {
          // جعل كل خلية قابلة للنقر لإظهار بطاقة الدين
          return DataCell(
            GestureDetector(
              onTap: () {
                // إظهار بطاقة الدين (نفس البطاقة الموجودة في قائمة الديون)
                _showDebtCard(debt, customer);
              },
              child: cell.child,
            ),
          );
        }).toList(),
      );
    }).toList();
  }

  // دالة تنسيق العملة مع فاصل الآلاف
  String _formatCurrencyWithThousands(double amount) {
    final formatter = NumberFormat('#,###', 'en_US');
    return formatter.format(amount.round());
  }

  // دالة للتحقق من وجود دفع جزئي
  bool _hasPartialPayment(Debt debt) {
    return debt.remainingAmount > 0 && debt.remainingAmount < debt.amount;
  }

  // دالة حساب إجمالي الكارتات
  int _calculateTotalCards(List<Debt> debts) {
    return debts.fold<int>(0, (sum, debt) => sum + debt.quantity);
  }

  // دالة حساب الكارتات المتبقية
  int _calculateRemainingCards(Debt debt) {
    if (debt.amount <= 0) return 0;

    // حساب نسبة المبلغ المتبقي من المبلغ الكلي
    final remainingRatio = debt.remainingAmount / debt.amount;

    // حساب الكارتات المتبقية بناءً على النسبة
    final remainingCards = (debt.quantity * remainingRatio).round();

    return remainingCards;
  }

  // دالة حساب الأيام منذ القيد
  String _getDaysSinceEntry(DateTime entryDate) {
    final now = DateTime.now();
    final difference = now.difference(entryDate).inDays;
    return 'منذ $difference يوم';
  }

  // دالة حساب الأيام المتبقية
  String _getDaysRemaining(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now).inDays;

    if (difference < 0) {
      return 'متأخر ${-difference} يوم';
    } else if (difference == 0) {
      return 'اليوم';
    } else {
      return 'باقي $difference يوم';
    }
  }

  // بناء العدادات المصغرة للجدول
  Widget _buildTableMiniCounters(List<Debt> debts,
      {List<Debt>? allCustomerDebts}) {
    // فلترة الديون النشطة فقط (التي لها مبلغ متبقي)
    final activeDebts =
        debts.where((debt) => debt.remainingAmount > 0).toList();

    // حساب الإحصائيات
    final totalAmount =
        activeDebts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // حساب الكمية والمبلغ المتأخر من جميع ديون العميل النشطة (وليس فقط المفلترة)
    // إذا تم تمرير allCustomerDebts، استخدمها، وإلا استخدم debts
    final debtsToCheckOverdue = allCustomerDebts ?? debts;
    final overdueDebts = debtsToCheckOverdue.where(
        (debt) => debt.remainingAmount > 0 && debt.dueDate.isBefore(now));

    final overdueQuantity =
        overdueDebts.fold<int>(0, (sum, debt) => sum + debt.quantity);
    final overdueAmount =
        overdueDebts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);
    final todayQuantity = activeDebts.where((debt) {
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      return debtEntryDate.isAtSameMomentAs(today);
    }).fold<int>(0, (sum, debt) => sum + debt.quantity);
    final yesterdayCount = activeDebts.where((debt) {
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      return debtEntryDate.isAtSameMomentAs(yesterday);
    }).length;
    final soonCount = activeDebts.where((debt) {
      final daysUntilDue = debt.dueDate.difference(today).inDays;
      return daysUntilDue >= 1 && daysUntilDue <= 3;
    }).length;

    // حساب أنواع الكروت (من الديون النشطة فقط)
    final Map<String, int> cardTypes = {};
    for (final debt in activeDebts) {
      final cardType = _convertCardTypeToArabic(debt.cardType);
      cardTypes[cardType] = (cardTypes[cardType] ?? 0) + debt.quantity;
    }

    // إنشاء قائمة العدادات - عداد اليوم أولاً
    final List<Widget> counters = [
      // عداد اليوم - يظهر دائماً في المقدمة
      _buildMiniCounterCard(
          'اليوم', todayQuantity.toString(), Icons.today, Colors.green),
      const SizedBox(width: 8),
      _buildMiniCounterCard(
          'الكارتات',
          _calculateTotalCards(activeDebts).toString(),
          Icons.credit_card,
          Colors.blue),
      const SizedBox(width: 8),
      _buildMiniCounterCard(
          'إجمالي المبلغ',
          _formatCurrencyWithThousands(totalAmount),
          Icons.attach_money,
          Colors.green),
      const SizedBox(width: 8),
    ];

    // إضافة عدادات الحالة
    if (overdueQuantity > 0) {
      counters.addAll([
        _buildOverdueCounterCard(overdueAmount, overdueQuantity),
        const SizedBox(width: 8),
      ]);
    }

    if (yesterdayCount > 0) {
      counters.addAll([
        _buildMiniCounterCard(
            'أمس', yesterdayCount.toString(), Icons.history, Colors.blue),
        const SizedBox(width: 8),
      ]);
    }

    if (soonCount > 0) {
      counters.addAll([
        _buildMiniCounterCard(
            'قريباً', soonCount.toString(), Icons.schedule, Colors.orange),
        const SizedBox(width: 8),
      ]);
    }

    // إضافة بطاقة إجمالي الكارتات مع المبلغ
    counters.addAll([
      _buildCombinedCounterCard(
        'إجمالي الكارتات',
        _calculateTotalCards(activeDebts).toString(),
        _formatCurrencyWithThousands(totalAmount),
        Icons.credit_card,
        Colors.teal,
      ),
      const SizedBox(width: 8),
    ]);

    // إضافة عدادات أنواع الكروت (أول 3 أنواع)
    if (cardTypes.isNotEmpty) {
      final sortedCardTypes = cardTypes.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      for (int i = 0; i < sortedCardTypes.length && i < 3; i++) {
        final entry = sortedCardTypes[i];

        // حساب المبلغ لهذا النوع من الكروت
        final typeAmount = activeDebts
            .where(
                (debt) => _convertCardTypeToArabic(debt.cardType) == entry.key)
            .fold<double>(0, (sum, debt) => sum + debt.remainingAmount);

        counters.addAll([
          _buildCombinedCounterCard(
            entry.key,
            entry.value.toString(),
            _formatCurrencyWithThousands(typeAmount),
            Icons.credit_card,
            Colors.purple,
          ),
          const SizedBox(width: 8),
        ]);
      }
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(children: counters),
    );
  }

  // بناء بطاقة عداد مصغر
  Widget _buildMiniCounterCard(
      String label, String value, IconData icon, Color color) {
    return Container(
      width: 100, // عرض ثابت لجميع العدادات
      height: 60, // ارتفاع ثابت لجميع العدادات
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 4),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة عداد مركبة (كمية + مبلغ)
  Widget _buildCombinedCounterCard(String label, String quantity, String amount,
      IconData icon, Color color) {
    return Container(
      width: 100, // عرض ثابت لجميع العدادات
      height: 60, // ارتفاع ثابت لجميع العدادات
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 4),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  quantity,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  amount,
                  style: const TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة عداد المتأخرة الأفقية
  Widget _buildOverdueCounterCard(double amount, int quantity) {
    return Container(
      width: 100, // عرض ثابت لجميع العدادات
      height: 60, // ارتفاع ثابت لجميع العدادات
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.warning, color: Colors.red, size: 16),
          const SizedBox(width: 4),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatCurrencyWithThousands(amount),
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '$quantity كارت',
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'متأخرة',
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقات التصنيف الزمني
  Widget _buildTimeClassificationCards() {
    // جمع جميع الديون من جميع العملاء
    final allDebts = <Debt>[];
    for (final customer in _filteredCustomers) {
      final customerDebts = _customerDebtsMap[customer] ?? [];
      allDebts.addAll(customerDebts);
    }

    if (allDebts.isEmpty) {
      return const SizedBox.shrink();
    }

    // تصنيف الديون حسب الوقت
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisWeekStart = today.subtract(Duration(days: today.weekday - 1));
    final thisMonthStart = DateTime(today.year, today.month);

    final Map<String, List<Debt>> timeCategories = {
      'الكل': allDebts,
      'متأخرة': [],
      'اليوم': [],
      'أمس': [],
      'قريباً': [],
      'هذا الأسبوع': [],
      'هذا الشهر': [],
    };

    for (final debt in allDebts) {
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final daysUntilDue = debtDueDate.difference(today).inDays;

      // تصنيف حسب تاريخ الاستحقاق
      if (daysUntilDue < 0) {
        timeCategories['متأخرة']!.add(debt);
      } else if (daysUntilDue >= 1 && daysUntilDue <= 3) {
        timeCategories['قريباً']!.add(debt);
      }

      // تصنيف حسب تاريخ الدخول
      if (debtEntryDate.isAtSameMomentAs(today)) {
        timeCategories['اليوم']!.add(debt);
      } else if (debtEntryDate.isAtSameMomentAs(yesterday)) {
        timeCategories['أمس']!.add(debt);
      } else if (debtEntryDate.isAfter(thisWeekStart) &&
          debtEntryDate.isBefore(today)) {
        timeCategories['هذا الأسبوع']!.add(debt);
      } else if (debtEntryDate.isAfter(thisMonthStart) &&
          debtEntryDate.isBefore(thisWeekStart)) {
        timeCategories['هذا الشهر']!.add(debt);
      }
    }

    return Container(
      margin: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Row(
              children: [
                Icon(
                  Icons.category,
                  size: 16,
                  color: Colors.teal.shade600,
                ),
                const SizedBox(width: 6),
                Text(
                  'التصنيف الزمني',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal.shade700,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 90,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: timeCategories.length,
              itemBuilder: (context, index) {
                final entry = timeCategories.entries.elementAt(index);
                final category = entry.key;
                final categoryDebts = entry.value;

                if (categoryDebts.isEmpty && category != 'الكل') {
                  return const SizedBox.shrink();
                }

                return Container(
                  margin: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
                  child: _buildTimeClassificationCard(
                      category, categoryDebts, timeCategories),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة تصنيف زمني واحدة
  Widget _buildTimeClassificationCard(String category, List<Debt> debts,
      Map<String, List<Debt>> allCategories) {
    final totalAmount =
        debts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);
    final totalQuantity =
        debts.fold<int>(0, (sum, debt) => sum + debt.quantity);

    MaterialColor cardColor;
    IconData cardIcon;

    switch (category) {
      case 'متأخرة':
        cardColor = Colors.red;
        cardIcon = Icons.warning;
        break;
      case 'اليوم':
        cardColor = Colors.green;
        cardIcon = Icons.today;
        break;
      case 'أمس':
        cardColor = Colors.indigo;
        cardIcon = Icons.history;
        break;
      case 'قريباً':
        cardColor = Colors.orange;
        cardIcon = Icons.schedule;
        break;
      case 'هذا الأسبوع':
        cardColor = Colors.blue;
        cardIcon = Icons.date_range;
        break;
      case 'هذا الشهر':
        cardColor = Colors.purple;
        cardIcon = Icons.calendar_month;
        break;
      default:
        cardColor = Colors.grey;
        cardIcon = Icons.category;
    }

    final isSelected = _selectedTimeCategory == category;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedTimeCategory = category;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 110,
          height: 80,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isSelected
                  ? [cardColor.shade200, cardColor.shade100]
                  : [cardColor.shade100, cardColor.shade50],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? cardColor.shade500 : cardColor.shade300,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: cardColor.withValues(alpha: isSelected ? 0.2 : 0.1),
                blurRadius: isSelected ? 6 : 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // الأيقونة والعنوان
              Flexible(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      cardIcon,
                      color: cardColor.shade700,
                      size: 14,
                    ),
                    const SizedBox(width: 3),
                    Flexible(
                      child: Text(
                        category,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: cardColor.shade800,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 2),
              // الكمية الإجمالية
              Text(
                '$totalQuantity',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: cardColor.shade700,
                ),
                maxLines: 1,
              ),
              // المبلغ
              Text(
                _formatCurrencyWithThousands(totalAmount),
                style: TextStyle(
                  fontSize: 9,
                  color: cardColor.shade600,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              // عدد الديون
              Text(
                '${debts.length} ديون',
                style: TextStyle(
                  fontSize: 8,
                  color: cardColor.shade500,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // فلترة الديون حسب التصنيف المحدد
  List<Debt> _getFilteredDebtsByCategory(List<Debt> allDebts, String category) {
    if (category == 'الكل') {
      return allDebts;
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisWeekStart = today.subtract(Duration(days: today.weekday - 1));
    final thisMonthStart = DateTime(today.year, today.month);

    return allDebts.where((debt) {
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final daysUntilDue = debtDueDate.difference(today).inDays;

      switch (category) {
        case 'متأخرة':
          return daysUntilDue < 0;
        case 'اليوم':
          return debtEntryDate.isAtSameMomentAs(today);
        case 'أمس':
          return debtEntryDate.isAtSameMomentAs(yesterday);
        case 'قريباً':
          return daysUntilDue >= 1 && daysUntilDue <= 3;
        case 'هذا الأسبوع':
          return debtEntryDate
                  .isAfter(thisWeekStart.subtract(const Duration(days: 1))) &&
              debtEntryDate.isBefore(today);
        case 'هذا الشهر':
          return debtEntryDate
                  .isAfter(thisMonthStart.subtract(const Duration(days: 1))) &&
              debtEntryDate.isBefore(thisWeekStart);
        default:
          return false;
      }
    }).toList();
  }

  // دالة لتحويل أسماء البطاقات إلى العربية
  String _convertCardTypeToArabic(String cardType) {
    final cleanType = cardType.trim().toLowerCase();

    // التعامل مع البطاقات المخصصة
    if (cleanType.startsWith('custom_')) {
      try {
        // استخراج الرقم من custom_X
        final customNumber = cleanType.replaceAll('custom_', '');
        switch (customNumber) {
          case '1':
          case '3':
            return 'أبو الستة'; // فئة 6000
          case '2':
          case '4':
            return 'آسيا'; // فئة 5000
          case '5':
          case '6':
            return 'زين'; // فئة 5000
          case '7':
          case '8':
            return 'أبو العشرة'; // فئة 10000
          default:
            return 'بطاقة مخصصة $customNumber';
        }
      } catch (e) {
        return cardType.isNotEmpty ? cardType : 'غير محدد';
      }
    }

    // التعامل مع الأنواع الافتراضية
    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia':
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      default:
        // إذا كان النص عربي، أعده كما هو
        if (_isArabicText(cardType)) {
          return cardType.isNotEmpty ? cardType : 'غير محدد';
        }
        // إذا كان إنجليزي ولم نجده، أعده كما هو
        return cardType.isNotEmpty ? cardType : 'غير محدد';
    }
  }

  // دالة للتحقق من النص العربي
  bool _isArabicText(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  // دالة لتنسيق التاريخ مع اسم اليوم بالعربية (سطر واحد)
  String _formatDateWithDayName(DateTime date, {bool includeTime = false}) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    String dayName;
    if (dateOnly.isAtSameMomentAs(today)) {
      dayName = 'اليوم';
    } else if (dateOnly.isAtSameMomentAs(yesterday)) {
      dayName = 'أمس';
    } else {
      // أسماء الأيام بالعربية
      const arabicDays = [
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت',
        'الأحد'
      ];
      dayName = arabicDays[date.weekday - 1];
    }

    // تنسيق التاريخ في سطر واحد
    final dateStr = '${date.day}/${date.month}/${date.year}';

    if (includeTime) {
      final timeStr = _formatTimeInArabic(date);
      return '$dayName $dateStr $timeStr';
    } else {
      return '$dayName $dateStr';
    }
  }

  // دالة لتنسيق الوقت بالعربية
  String _formatTimeInArabic(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;
    final minuteStr = minute.toString().padLeft(2, '0');

    if (hour == 0) {
      return '12:$minuteStr منتصف الليل';
    } else if (hour < 12) {
      final hourStr = hour.toString().padLeft(2, '0');
      return '$hourStr:$minuteStr صباحاً';
    } else if (hour == 12) {
      return '12:$minuteStr ظهراً';
    } else {
      final hour12 = hour - 12;
      final hourStr = hour12.toString().padLeft(2, '0');
      return '$hourStr:$minuteStr مساءً';
    }
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: StatefulBuilder(
        builder: (context, setSearchState) {
          return TextField(
            controller: _searchController,
            onChanged: (value) {
              _filterCustomers(value);
              setSearchState(() {}); // تحديث حالة زر المسح
            },
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintText: 'البحث عن عميل...',
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 16,
                fontWeight: FontWeight.normal,
              ),
              prefixIcon: Icon(Icons.search, color: Colors.blue.shade600),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear, color: Colors.red.shade400),
                      onPressed: () {
                        _searchController.clear();
                        _filterCustomers('');
                        setSearchState(() {}); // تحديث حالة زر المسح
                      },
                      tooltip: 'مسح البحث',
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد ديون',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة ديون للعملاء',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  // دالة بناء شريط أدوات التحديد المتعدد
  Widget _buildMultiSelectToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _multiSelectMode == 'payment' ? Colors.green : Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // أيقونة النوع
          Icon(
            _multiSelectMode == 'payment' ? Icons.payment : Icons.delete,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),

          // النص
          Expanded(
            child: Text(
              _multiSelectMode == 'payment'
                  ? 'تحديد للتسديد (${_selectedDebtIds.length})'
                  : 'تحديد للحذف (${_selectedDebtIds.length})',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // زر التنفيذ
          if (_selectedDebtIds.isNotEmpty) ...[
            ElevatedButton(
              onPressed: () {
                if (_multiSelectMode == 'payment') {
                  _processSelectedPayments();
                } else {
                  _deleteSelectedDebts();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor:
                    _multiSelectMode == 'payment' ? Colors.green : Colors.red,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                _multiSelectMode == 'payment' ? 'تسديد' : 'حذف',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(width: 8),
          ],

          // زر الإلغاء
          IconButton(
            onPressed: () {
              setState(() {
                _isMultiSelectMode = false;
                _selectedDebtIds.clear();
              });
            },
            icon: const Icon(Icons.close, color: Colors.white),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  // دالة معالجة التسديدات المحددة
  void _processSelectedPayments() {
    // هنا يمكن إضافة منطق معالجة التسديدات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تسديد ${_selectedDebtIds.length} دين'),
        backgroundColor: Colors.green,
      ),
    );

    setState(() {
      _isMultiSelectMode = false;
      _selectedDebtIds.clear();
    });
  }

  // دالة معالجة حذف الديون المحددة
  void _deleteSelectedDebts() {
    // هنا يمكن إضافة منطق حذف الديون
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حذف ${_selectedDebtIds.length} دين'),
        backgroundColor: Colors.red,
      ),
    );

    setState(() {
      _isMultiSelectMode = false;
      _selectedDebtIds.clear();
    });
  }

  // دالة حساب المبلغ الإجمالي للديون المحددة
  double _calculateSelectedDebtsTotal() {
    double total = 0.0;
    for (final customer in _filteredCustomers) {
      final customerDebts = _customerDebtsMap[customer] ?? [];
      for (final debt in customerDebts) {
        if (_selectedDebtIds.contains(debt.id)) {
          total += debt.remainingAmount;
        }
      }
    }
    return total;
  }

  // بناء الشريط العلوي لإحصائيات التحديد
  Widget _buildSelectionTopBar() {
    final selectedCount = _selectedDebtIds.length;
    final totalAmount = _calculateSelectedDebtsTotal();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.blue.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.blue.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تم تحديد $selectedCount دين',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'المبلغ الإجمالي: ${_formatCurrencyWithThousands(totalAmount)} د.ع',
                  style: const TextStyle(
                    color: Colors.black54,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _isMultiSelectMode = false;
                _selectedDebtIds.clear();
              });
            },
            icon: Icon(
              Icons.close,
              color: Colors.blue.shade600,
            ),
          ),
        ],
      ),
    );
  }

  // بناء الشريط السفلي لأدوات التحديد
  Widget _buildSelectionBottomBar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(color: Colors.grey.shade300),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            // زر التسديد
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _processSelectedPayments();
                },
                icon: const Icon(Icons.payment, size: 20),
                label: const Text(
                  'تسديد',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // زر الحذف
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _deleteSelectedDebts();
                },
                icon: const Icon(Icons.delete, size: 20),
                label: const Text(
                  'حذف',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة إظهار بطاقة الدين (نفس البطاقة الموجودة في قائمة الديون)
  void _showDebtCard(Debt debt, Customer customer) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.3,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // عنوان النافذة
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.receipt_long, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    Text(
                      'تفاصيل الدين',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              // بطاقة الدين قابلة للتمرير
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      DebtCard(
                        debt: debt,
                        isStandardView: true,
                        disableLongPress: true,
                      ),
                      const SizedBox(height: 20), // مساحة إضافية في الأسفل
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
