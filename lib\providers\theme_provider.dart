import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  // Constructor يحمل الإعدادات فوراً
  ThemeProvider() {
    _loadThemeSettingsSync();
  }
  bool _isDarkMode = false;
  bool _isLoaded = false;

  bool get isDarkMode => _isDarkMode;
  bool get isLoaded => _isLoaded;

  // تحميل إعدادات الثيم بشكل متزامن
  void _loadThemeSettingsSync() {
    loadThemeSettings();
  }

  // تحميل إعدادات الثيم من SharedPreferences
  Future<void> loadThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isDarkMode = prefs.getBool('dark_mode') ?? false;
      _isLoaded = true;
      notifyListeners();
      debugPrint('✅ تم تحميل إعدادات الثيم: الوضع الليلي = $_isDarkMode');
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الثيم: $e');
      _isLoaded = true;
      notifyListeners();
    }
  }

  // تبديل الوضع الليلي
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
    await _saveThemeSettings();
  }

  // تحديث الوضع الليلي
  Future<void> setDarkMode(bool value) async {
    debugPrint('🌙 تحديث الوضع الليلي: $value (الحالي: $_isDarkMode)');
    if (_isDarkMode != value) {
      _isDarkMode = value;
      debugPrint('🔄 تم تغيير الوضع الليلي إلى: $_isDarkMode');
      notifyListeners();
      await _saveThemeSettings();
    } else {
      debugPrint('⚠️ نفس القيمة، لا حاجة للتحديث');
    }
  }

  // حفظ إعدادات الثيم
  Future<void> _saveThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('dark_mode', _isDarkMode);
      debugPrint('💾 تم حفظ إعدادات الثيم: الوضع الليلي = $_isDarkMode');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات الثيم: $e');
    }
  }

  // الحصول على الثيم الفاتح
  ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      primarySwatch: Colors.teal,
      primaryColor: const Color(0xFF00695C),
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF00695C),
        primary: const Color(0xFF00695C),
        secondary: const Color(0xFF4CAF50),
        tertiary: const Color(0xFF2196F3),
        surface: const Color(0xFFF8F9FA),
        background: Colors.white,
        onBackground: Colors.black87,
        onSurface: Colors.black87,
      ),
      scaffoldBackgroundColor: Colors.white,
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        iconTheme: IconThemeData(color: Colors.white),
      ),
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 2,
        shadowColor: Colors.grey.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF00695C),
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.grey.shade50,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF00695C),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      useMaterial3: true,
    );
  }

  // الحصول على الثيم الداكن
  ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primarySwatch: Colors.teal,
      primaryColor: const Color(0xFF00695C),
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF00695C),
        brightness: Brightness.dark,
        primary: const Color(0xFF4DB6AC),
        secondary: const Color(0xFF66BB6A),
        tertiary: const Color(0xFF42A5F5),
        surface: const Color(0xFF1E1E1E),
        background: const Color(0xFF121212),
        onBackground: Colors.white,
        onSurface: Colors.white,
      ),
      scaffoldBackgroundColor: const Color(0xFF121212),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1E1E1E),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        iconTheme: IconThemeData(color: Colors.white),
      ),
      cardTheme: CardThemeData(
        color: const Color(0xFF1E1E1E),
        elevation: 4,
        shadowColor: Colors.black.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4DB6AC),
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFF2C2C2C),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF404040)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF404040)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF4DB6AC),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      useMaterial3: true,
    );
  }

  // الحصول على الثيم الحالي
  ThemeData get currentTheme => _isDarkMode ? darkTheme : lightTheme;
}
