import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/customer.dart';
import '../providers/debt_provider.dart';
import '../widgets/debts_tab.dart';

class CustomerDebtsScreen extends StatefulWidget {
  const CustomerDebtsScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerDebtsScreen> createState() => _CustomerDebtsScreenState();
}

class _CustomerDebtsScreenState extends State<CustomerDebtsScreen> {
  // نوع العرض الحالي
  DebtCardViewType _currentViewType = DebtCardViewType.standard;
  // مفتاح للوصول إلى DebtsTab
  final GlobalKey _debtsTabKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadSavedViewType();

    // Load customer debts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerDebts(widget.customer.id!);
    });
  }

  // دالة لتحديث نوع العرض من DebtsTab
  void _onViewTypeChanged(DebtCardViewType viewType) {
    setState(() {
      _currentViewType = viewType;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh debts when returning to this screen
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.refreshCurrentCustomerDebts();
      });
    }
  }

  // دالة لتحميل نوع العرض المحفوظ
  Future<void> _loadSavedViewType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedViewType = prefs.getString('debt_view_type');

      if (savedViewType != null) {
        final viewType = DebtCardViewType.values.firstWhere(
          (e) => e.name == savedViewType,
          orElse: () => DebtCardViewType.standard,
        );
        if (mounted) {
          setState(() {
            _currentViewType = viewType;
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نوع العرض: $e');
    }
  }

  // دالة لحفظ نوع العرض المختار
  Future<void> _saveViewType(DebtCardViewType viewType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('debt_view_type', viewType.name);
    } catch (e) {
      debugPrint('خطأ في حفظ نوع العرض: $e');
    }
  }

  // الحصول على أيقونة نوع العرض
  IconData _getViewTypeIcon(DebtCardViewType viewType) {
    switch (viewType) {
      case DebtCardViewType.standard:
        return Icons.view_agenda;
      case DebtCardViewType.compact:
        return Icons.view_list;
      case DebtCardViewType.mini:
        return Icons.view_stream;
      case DebtCardViewType.table:
        return Icons.table_chart;
      case DebtCardViewType.grid:
        return Icons.grid_view;
      case DebtCardViewType.timeline:
        return Icons.timeline;
    }
  }

  // بناء عنصر قائمة نوع العرض
  Widget _buildViewTypeMenuItem(
    DebtCardViewType viewType,
    IconData icon,
    String title,
    String subtitle,
  ) {
    final isSelected = _currentViewType == viewType;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade50 : null,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isSelected ? Colors.blue.shade600 : Colors.grey.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? Colors.blue.shade600 : Colors.black87,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            Icon(
              Icons.check,
              color: Colors.blue.shade600,
              size: 16,
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة الديون'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر تغيير نوع العرض في AppBar
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: PopupMenuButton<DebtCardViewType>(
              icon: Icon(
                _getViewTypeIcon(_currentViewType),
                color: Colors.white,
                size: 24,
              ),
              tooltip: 'تغيير نوع العرض',
              onSelected: (DebtCardViewType viewType) {
                setState(() {
                  _currentViewType = viewType;
                });
                _saveViewType(viewType);
                // تحديث نوع العرض في DebtsTab مباشرة
                final debtsTabState = _debtsTabKey.currentState;
                if (debtsTabState != null) {
                  (debtsTabState as dynamic).updateViewType(viewType);
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: DebtCardViewType.standard,
                  child: _buildViewTypeMenuItem(
                    DebtCardViewType.standard,
                    Icons.view_agenda,
                    'عرض عادي',
                    'البطاقات العادية مع جميع التفاصيل',
                  ),
                ),
                PopupMenuItem(
                  value: DebtCardViewType.compact,
                  child: _buildViewTypeMenuItem(
                    DebtCardViewType.compact,
                    Icons.view_list,
                    'عرض مضغوط',
                    'بطاقات مضغوطة مع جميع التفاصيل',
                  ),
                ),
                PopupMenuItem(
                  value: DebtCardViewType.mini,
                  child: _buildViewTypeMenuItem(
                    DebtCardViewType.mini,
                    Icons.view_stream,
                    'عرض مصغر',
                    '5 بطاقات صغيرة في صف واحد',
                  ),
                ),
                PopupMenuItem(
                  value: DebtCardViewType.table,
                  child: _buildViewTypeMenuItem(
                    DebtCardViewType.table,
                    Icons.table_chart,
                    'عرض جدولي',
                    'جدول مع جميع التفاصيل والأعمدة',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: DebtsTab(
        key: _debtsTabKey,
        customer: widget.customer,
        onViewTypeChanged: _onViewTypeChanged,
      ),
    );
  }
}
