import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../widgets/add_debt_bottom_sheet.dart';
import '../utils/number_formatter.dart';
import '../database/database_helper.dart';
import 'customer_debts_screen.dart';
import 'customer_payments_screen.dart';
import 'customer_info_screen.dart';
import 'customer_daily_debts_screen.dart';
import 'account_statement_screen.dart';
import 'customer_limit_screen.dart';
import 'overdue_debts_screen.dart';
import 'due_today_debts_screen.dart';

// أنواع عرض البطاقات في صفحة تفاصيل العميل
enum CustomerDetailViewType {
  standard, // العرض العادي
  grid, // عرض شبكي
}

class CustomerDetailScreen extends StatefulWidget {
  const CustomerDetailScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with TickerProviderStateMixin {
  // نوع العرض الحالي
  CustomerDetailViewType _currentViewType = CustomerDetailViewType.standard;

  // TabController للإحصائيات
  TabController? _statisticsTabController;

  @override
  void initState() {
    super.initState();
    // تهيئة TabController للإحصائيات
    _statisticsTabController = TabController(length: 2, vsync: this);
    // تحميل نوع العرض المحفوظ
    _loadSavedViewType();
    // Load customer debts and payments
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      await debtProvider.loadCustomerDebts(widget.customer.id!);
      await debtProvider.loadCustomerPayments(widget.customer.id!);
      // إجبار إعادة بناء الواجهة
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _statisticsTabController?.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh debts and payments when returning to this screen
    _refreshData();
  }

  Future<void> _refreshData() async {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    await debtProvider.loadCustomerDebts(widget.customer.id!);
    await debtProvider.loadCustomerPayments(widget.customer.id!);
    // إجبار إعادة بناء الواجهة
    if (mounted) {
      setState(() {});
    }
  }

  // تحميل نوع العرض المحفوظ
  Future<void> _loadSavedViewType() async {
    final prefs = await SharedPreferences.getInstance();
    final savedViewType = prefs.getInt('customer_detail_view_type') ?? 0;
    setState(() {
      _currentViewType = CustomerDetailViewType.values[savedViewType];
    });
  }

  // حفظ نوع العرض
  Future<void> _saveViewType(CustomerDetailViewType viewType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('customer_detail_view_type', viewType.index);
  }

  // الحصول على أيقونة نوع العرض
  IconData _getViewTypeIcon(CustomerDetailViewType viewType) {
    switch (viewType) {
      case CustomerDetailViewType.standard:
        return Icons.view_agenda;
      case CustomerDetailViewType.grid:
        return Icons.grid_view;
    }
  }

  // بناء عنصر قائمة نوع العرض
  Widget _buildViewTypeMenuItem(
    CustomerDetailViewType viewType,
    IconData icon,
    String title,
    String subtitle,
  ) {
    final isSelected = _currentViewType == viewType;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isSelected ? Colors.blue : Colors.grey.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    color: isSelected ? Colors.blue : Colors.black87,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            const Icon(
              Icons.check_circle,
              color: Colors.blue,
              size: 18,
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.customer.name),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر تغيير نوع العرض
          PopupMenuButton<CustomerDetailViewType>(
            icon: Icon(_getViewTypeIcon(_currentViewType), color: Colors.white),
            tooltip: 'تغيير نوع العرض',
            onSelected: (CustomerDetailViewType viewType) {
              setState(() {
                _currentViewType = viewType;
              });
              _saveViewType(viewType);
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: CustomerDetailViewType.standard,
                child: _buildViewTypeMenuItem(
                  CustomerDetailViewType.standard,
                  Icons.view_agenda,
                  'عرض عادي',
                  'البطاقات العادية',
                ),
              ),
              PopupMenuItem(
                value: CustomerDetailViewType.grid,
                child: _buildViewTypeMenuItem(
                  CustomerDetailViewType.grid,
                  Icons.grid_view,
                  'عرض شبكي',
                  'بطاقات في شبكة',
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Customer Info Card
            Container(
              margin: const EdgeInsets.all(20),
              child: _buildSimpleCustomerInfoCard(),
            ),

            // Navigation Buttons
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCardsView(),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // بناء عرض البطاقات حسب النوع المحدد
  Widget _buildCardsView() {
    switch (_currentViewType) {
      case CustomerDetailViewType.standard:
        return _buildStandardView();
      case CustomerDetailViewType.grid:
        return _buildGridView();
    }
  }

  // العرض العادي
  Widget _buildStandardView() {
    return Column(
      children: [
        // إضافة دين جديد
        _buildAddDebtCard(),
        const SizedBox(height: 16),

        // قائمة الديون
        _buildAdvancedActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerDebtsScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.receipt_long,
          title: 'قائمة الديون',
          color: Colors.blue,
          gradient: LinearGradient(
            colors: [Colors.blue.shade600, Colors.blue.shade700],
          ),
          isDebtCard: true,
          isFullWidth: true,
        ),

        const SizedBox(height: 16),

        // قائمة التسديدات
        _buildAdvancedActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerPaymentsScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.payment,
          title: 'قائمة التسديدات',
          color: Colors.green,
          gradient: LinearGradient(
            colors: [Colors.green.shade600, Colors.green.shade700],
          ),
          isDebtCard: false,
          isFullWidth: true,
        ),

        const SizedBox(height: 16),

        // كشف الحساب
        _buildAdvancedActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    AccountStatementScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.account_balance,
          title: 'كشف الحساب',
          color: Colors.purple,
          gradient: LinearGradient(
            colors: [Colors.purple.shade600, Colors.purple.shade700],
          ),
          isDebtCard: false,
          isFullWidth: true,
        ),

        const SizedBox(height: 16),

        // إدارة السقف
        _buildAdvancedActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerLimitScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.trending_up,
          title: 'إدارة السقف',
          color: Colors.orange,
          gradient: LinearGradient(
            colors: [Colors.orange.shade600, Colors.orange.shade700],
          ),
          isDebtCard: false,
          isFullWidth: true,
        ),

        const SizedBox(height: 16),

        // الإحصائيات
        _buildAdvancedStatisticsCard(
          onPressed: () => _showStatisticsBottomSheet(context),
        ),
      ],
    );
  }

  // العرض الشبكي
  Widget _buildGridView() {
    return Column(
      children: [
        Column(
          children: [
            // الشبكة للبطاقات الأربع الأولى
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
              children: [
                _buildAdvancedActionCard(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            CustomerDebtsScreen(customer: widget.customer),
                      ),
                    );
                  },
                  icon: Icons.receipt_long,
                  title: 'قائمة الديون',
                  color: Colors.blue,
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade600, Colors.blue.shade700],
                  ),
                  isDebtCard: true,
                ),
                _buildAdvancedActionCard(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            CustomerPaymentsScreen(customer: widget.customer),
                      ),
                    );
                  },
                  icon: Icons.payment,
                  title: 'قائمة التسديدات',
                  color: Colors.green,
                  gradient: LinearGradient(
                    colors: [Colors.green.shade600, Colors.green.shade700],
                  ),
                  isDebtCard: false,
                ),
                _buildAdvancedActionCard(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            AccountStatementScreen(customer: widget.customer),
                      ),
                    );
                  },
                  icon: Icons.account_balance,
                  title: 'كشف الحساب',
                  color: Colors.purple,
                  gradient: LinearGradient(
                    colors: [Colors.purple.shade600, Colors.purple.shade700],
                  ),
                  isDebtCard: false,
                ),
                _buildAdvancedActionCard(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            CustomerLimitScreen(customer: widget.customer),
                      ),
                    );
                  },
                  icon: Icons.trending_up,
                  title: 'إدارة السقف',
                  color: Colors.orange,
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade600, Colors.orange.shade700],
                  ),
                  isDebtCard: false,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // بطاقة الإحصائيات العريضة
            _buildAdvancedStatisticsCard(
              onPressed: () => _showStatisticsBottomSheet(context),
            ),

            const SizedBox(height: 16),

            // زر إضافة الدين في الأسفل
            _buildAddDebtCard(),
          ],
        ),
      ],
    );
  }

  // بناء بطاقة إضافة دين جديد
  Widget _buildAddDebtCard() {
    return Container(
      width: double.infinity,
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.cyan.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            await showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) =>
                  AddDebtBottomSheet(customer: widget.customer),
            );

            // تحديث البيانات عند العودة
            if (mounted) {
              await _refreshData();
            }
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icon Section
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.cyan.shade50,
                    borderRadius: BorderRadius.circular(14),
                    border:
                        Border.all(color: Colors.black.withValues(alpha: 0.1)),
                  ),
                  child: const Icon(
                    Icons.add_circle,
                    size: 28,
                    color: Colors.teal,
                  ),
                ),

                const SizedBox(width: 16),

                // Content Section
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'إضافة دين جديد',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'إضافة كارت جديد لهذا العميل',
                        style: TextStyle(fontSize: 10, color: Colors.black54),
                      ),
                    ],
                  ),
                ),

                // Arrow Section
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.cyan.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border:
                        Border.all(color: Colors.black.withValues(alpha: 0.1)),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.teal,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة معلومات العميل المبسطة
  Widget _buildSimpleCustomerInfoCard() {
    return FutureBuilder<List<Debt>>(
      key: ValueKey(
        'customer_info_${widget.customer.id}_${DateTime.now().millisecondsSinceEpoch}',
      ),
      future: Future.value(
        Provider.of<DebtProvider>(
          context,
          listen: false,
        ).debts.where((debt) => debt.customerId == widget.customer.id).toList(),
      ),
      builder: (context, snapshot) {
        final customerDebts = snapshot.data ?? [];

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      CustomerInfoScreen(customer: widget.customer),
                ),
              );
            },
            borderRadius: BorderRadius.circular(20),
            child: Row(
              children: [
                // Icon Section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Icon(
                    Icons.person,
                    size: 32,
                    color: Colors.grey.shade600,
                  ),
                ),

                const SizedBox(width: 16),

                // Content Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'معلومات العميل',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        widget.customer.name,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.black54,
                        ),
                      ),
                      if (widget.customer.phone != null &&
                          widget.customer.phone!.isNotEmpty) ...[
                        const SizedBox(height: 1),
                        Text(
                          widget.customer.phone!,
                          style: const TextStyle(
                            fontSize: 11,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Arrow and Stats
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${customerDebts.fold(0, (sum, debt) => sum + debt.quantity)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'عرض التفاصيل',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة عمل متقدمة
  Widget _buildAdvancedActionCard({
    required VoidCallback onPressed,
    required IconData icon,
    required String title,
    required Color color,
    required Gradient gradient,
    required bool isDebtCard,
    bool isFullWidth = false,
  }) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      height: isFullWidth ? 100 : 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.cyan.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: isFullWidth
                ? Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.cyan.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                              color: Colors.black.withValues(alpha: 0.1)),
                        ),
                        child: Icon(
                          icon,
                          size: 24,
                          color: color,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              title,
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 2),
                            const Text(
                              'اضغط للعرض',
                              style: TextStyle(
                                fontSize: 8,
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: color,
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الأيقونة الملونة في الوسط
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: gradient,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: color.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          icon,
                          size: 32,
                          color: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 12),

                      // العنوان في الوسط تحت الأيقونة
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                          letterSpacing: 0.2,
                          height: 1.2,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة إحصائيات متقدمة
  Widget _buildAdvancedStatisticsCard({required VoidCallback onPressed}) {
    return FutureBuilder<List<Debt>>(
      future: DatabaseHelper().getCustomerDebts(widget.customer.id!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: double.infinity,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, Colors.cyan.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
            ),
            child: const Center(
              child: CircularProgressIndicator(color: Colors.black87),
            ),
          );
        }

        return Container(
          width: double.infinity,
          height: 80,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.white, Colors.cyan.shade50],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                blurRadius: 15,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // الأيقونة
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.purple.shade100,
                            Colors.purple.shade50
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: Colors.purple.withValues(alpha: 0.3)),
                      ),
                      child: const Icon(
                        Icons.analytics,
                        size: 24,
                        color: Colors.purple,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // المحتوى
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'الإحصائيات التفصيلية',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                              letterSpacing: 0.3,
                              height: 1.2,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'عرض تحليل شامل للديون والتسديدات',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.black54,
                              letterSpacing: 0.2,
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.purple,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showStatisticsBottomSheet(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _StatisticsTabScreen(
          customer: widget.customer,
        ),
      ),
    );
  }
}

// شاشة الإحصائيات مع التبويبات
class _StatisticsTabScreen extends StatefulWidget {
  const _StatisticsTabScreen({required this.customer});
  final Customer customer;

  @override
  State<_StatisticsTabScreen> createState() => _StatisticsTabScreenState();
}

class _StatisticsTabScreenState extends State<_StatisticsTabScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إحصائيات ${widget.customer.name}'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.analytics),
              text: 'إحصائيات الديون',
            ),
            Tab(
              icon: Icon(Icons.payment),
              text: 'إحصائيات السداد',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _StatisticsContent(customer: widget.customer),
          _PaymentStatisticsContent(customer: widget.customer),
        ],
      ),
    );
  }
}

// كلاس منفصل لمحتوى إحصائيات الديون
class _StatisticsContent extends StatelessWidget {
  const _StatisticsContent({required this.customer});
  final Customer customer;

  // فتح صفحة الديون المتأخرة
  void _showOverdueDebtsDialog(BuildContext context, List<Debt> overdueDebts) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OverdueDebtsScreen(
          customer: customer,
          overdueDebts: overdueDebts,
        ),
      ),
    );
  }

  // فتح صفحة الديون المستحقة اليوم
  void _showDueTodayDebtsDialog(
      BuildContext context, List<Debt> dueTodayDebts) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DueTodayDebtsScreen(
          customer: customer,
          dueTodayDebts: dueTodayDebts,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CardTypeProvider>(
      builder: (context, cardTypeProvider, child) {
        return FutureBuilder<List<Debt>>(
          key: ValueKey(
            'statistics_content_${customer.id}_${DateTime.now().millisecondsSinceEpoch}',
          ),
          future: Future.value(
            Provider.of<DebtProvider>(context, listen: false)
                .debts
                .where((debt) => debt.customerId == customer.id)
                .toList(),
          ),
          builder: (context, snapshot) {
            final customerDebts = snapshot.data ?? [];

            if (customerDebts.isEmpty) {
              return const Center(
                child: Text(
                  'لا توجد ديون لهذا العميل',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              );
            }

            final now = DateTime.now();
            final today = DateTime(now.year, now.month, now.day);
            final yesterday = today.subtract(const Duration(days: 1));
            final weekStart = today.subtract(Duration(days: today.weekday - 1));
            final monthStart = DateTime(now.year, now.month);
            final last30Days = today.subtract(const Duration(days: 30));

            // حسابات اليوم (المبيعات فقط - ديون العملاء لك)
            final todayDebts = customerDebts.where((debt) {
              final debtDate = DateTime(
                debt.entryDate.year,
                debt.entryDate.month,
                debt.entryDate.day,
              );
              return debtDate.isAtSameMomentAs(today) &&
                  debt.direction == DebtDirection.customerOwesMe;
            }).toList();
            final todaySales = todayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final todayCount =
                todayDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حساب تفاصيل الكارتات لمبيعات اليوم
            final todayCardTypeStats = <String, int>{};
            for (final debt in todayDebts) {
              final displayName =
                  _getCardTypeDisplayName(debt.cardType, cardTypeProvider);
              todayCardTypeStats[displayName] =
                  (todayCardTypeStats[displayName] ?? 0) + debt.quantity;
            }

            // تكوين نص تفاصيل الكارتات لمبيعات اليوم
            String todayCardDetails = '';
            if (todayCardTypeStats.isNotEmpty) {
              final details = todayCardTypeStats.entries.map((entry) {
                return '${entry.key}: ${entry.value}';
              }).join('\n');
              todayCardDetails = details;
            } else {
              todayCardDetails = '$todayCount كارت';
            }

            // حسابات الأمس (المبيعات فقط - ديون العملاء لك)
            final yesterdayDebts = customerDebts.where((debt) {
              final debtDate = DateTime(
                debt.entryDate.year,
                debt.entryDate.month,
                debt.entryDate.day,
              );
              return debtDate.isAtSameMomentAs(yesterday) &&
                  debt.direction == DebtDirection.customerOwesMe;
            }).toList();
            final yesterdaySales = yesterdayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final yesterdayCount =
                yesterdayDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حساب تفاصيل الكارتات لمبيعات الأمس
            final yesterdayCardTypeStats = <String, int>{};
            for (final debt in yesterdayDebts) {
              final displayName =
                  _getCardTypeDisplayName(debt.cardType, cardTypeProvider);
              yesterdayCardTypeStats[displayName] =
                  (yesterdayCardTypeStats[displayName] ?? 0) + debt.quantity;
            }

            // تكوين نص تفاصيل الكارتات لمبيعات الأمس
            String yesterdayCardDetails = '';
            if (yesterdayCardTypeStats.isNotEmpty) {
              final details = yesterdayCardTypeStats.entries.map((entry) {
                return '${entry.key}: ${entry.value}';
              }).join('\n');
              yesterdayCardDetails = details;
            } else {
              yesterdayCardDetails = '$yesterdayCount كارت';
            }

            // حسابات الأسبوع (المبيعات فقط - ديون العملاء لك)
            final weekDebts = customerDebts
                .where((debt) =>
                    debt.entryDate.isAfter(weekStart) &&
                    debt.direction == DebtDirection.customerOwesMe)
                .toList();
            final weekSales = weekDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final weekCount =
                weekDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حساب تفاصيل الكارتات لمبيعات الأسبوع
            final weekCardTypeStats = <String, int>{};
            for (final debt in weekDebts) {
              final displayName =
                  _getCardTypeDisplayName(debt.cardType, cardTypeProvider);
              weekCardTypeStats[displayName] =
                  (weekCardTypeStats[displayName] ?? 0) + debt.quantity;
            }

            // تكوين نص تفاصيل الكارتات لمبيعات الأسبوع
            String weekCardDetails = '';
            if (weekCardTypeStats.isNotEmpty) {
              final details = weekCardTypeStats.entries.map((entry) {
                return '${entry.key}: ${entry.value}';
              }).join('\n');
              weekCardDetails = details;
            } else {
              weekCardDetails = '$weekCount كارت';
            }

            // حسابات الشهر (المبيعات فقط - ديون العملاء لك)
            final monthDebts = customerDebts
                .where((debt) =>
                    debt.entryDate.isAfter(monthStart) &&
                    debt.direction == DebtDirection.customerOwesMe)
                .toList();
            final monthSales = monthDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final monthCount =
                monthDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حساب تفاصيل الكارتات لمبيعات الشهر
            final monthCardTypeStats = <String, int>{};
            for (final debt in monthDebts) {
              final displayName =
                  _getCardTypeDisplayName(debt.cardType, cardTypeProvider);
              monthCardTypeStats[displayName] =
                  (monthCardTypeStats[displayName] ?? 0) + debt.quantity;
            }

            // تكوين نص تفاصيل الكارتات لمبيعات الشهر
            String monthCardDetails = '';
            if (monthCardTypeStats.isNotEmpty) {
              final details = monthCardTypeStats.entries.map((entry) {
                return '${entry.key}: ${entry.value}';
              }).join('\n');
              monthCardDetails = details;
            } else {
              monthCardDetails = '$monthCount كارت';
            }

            // نشاط العميل (آخر 30 يوم)
            final recentDebts = customerDebts
                .where((debt) => debt.entryDate.isAfter(last30Days))
                .toList();
            final activityPercentage = customerDebts.isNotEmpty
                ? (recentDebts.length / customerDebts.length * 100)
                : 0.0;

            // إحصائيات أنواع الكروت مع المبالغ والكمية الصحيحة
            final cardTypeStats = <String, Map<String, dynamic>>{};
            for (final debt in customerDebts) {
              if (!cardTypeStats.containsKey(debt.cardType)) {
                cardTypeStats[debt.cardType] = {
                  'count': 0,
                  'amount': 0.0,
                  'quantity': 0,
                };
              }
              cardTypeStats[debt.cardType]!['count'] =
                  (cardTypeStats[debt.cardType]!['count'] as int) + 1;
              cardTypeStats[debt.cardType]!['amount'] =
                  (cardTypeStats[debt.cardType]!['amount'] as double) +
                      debt.amount;
              cardTypeStats[debt.cardType]!['quantity'] =
                  (cardTypeStats[debt.cardType]!['quantity'] as int) +
                      debt.quantity;
            }
            final sortedCardTypes = cardTypeStats.entries.toList()
              ..sort(
                (a, b) => (b.value['count'] as int).compareTo(
                  a.value['count'] as int,
                ),
              );

            // الديون المتأخرة
            final overdueDebts = customerDebts.where((debt) {
              if (debt.status == DebtStatus.paid) return false;
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              return dueDate.isBefore(today);
            }).toList();
            final overdueAmount = overdueDebts.fold(
              0.0,
              (sum, debt) => sum + debt.remainingAmount,
            );

            // حساب تفاصيل الكارتات للديون المتأخرة
            final overdueCardTypeStats = <String, int>{};
            for (final debt in overdueDebts) {
              final displayName =
                  _getCardTypeDisplayName(debt.cardType, cardTypeProvider);
              overdueCardTypeStats[displayName] =
                  (overdueCardTypeStats[displayName] ?? 0) + debt.quantity;
            }

            // تكوين نص تفاصيل الكارتات المتأخرة
            String overdueCardDetails = '';
            if (overdueCardTypeStats.isNotEmpty) {
              final details = overdueCardTypeStats.entries.map((entry) {
                return '${entry.key}: ${entry.value}';
              }).join('\n');
              overdueCardDetails = details;
            } else {
              overdueCardDetails = '${overdueDebts.length} دين';
            }

            // الديون المستحقة اليوم
            final dueTodayDebts = customerDebts.where((debt) {
              if (debt.status == DebtStatus.paid) return false;
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              return dueDate.isAtSameMomentAs(today);
            }).toList();
            final dueTodayAmount = dueTodayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.remainingAmount,
            );

            // حساب تفاصيل الكارتات للديون المستحقة اليوم
            final dueTodayCardTypeStats = <String, int>{};
            for (final debt in dueTodayDebts) {
              final displayName =
                  _getCardTypeDisplayName(debt.cardType, cardTypeProvider);
              dueTodayCardTypeStats[displayName] =
                  (dueTodayCardTypeStats[displayName] ?? 0) + debt.quantity;
            }

            // تكوين نص تفاصيل الكارتات المستحقة اليوم
            String dueTodayCardDetails = '';
            if (dueTodayCardTypeStats.isNotEmpty) {
              final details = dueTodayCardTypeStats.entries.map((entry) {
                return '${entry.key}: ${entry.value}';
              }).join('\n');
              dueTodayCardDetails = details;
            } else {
              dueTodayCardDetails = '${dueTodayDebts.length} دين';
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // قسم أنواع الكروت
                  if (sortedCardTypes.isNotEmpty) ...[
                    const Text(
                      'أنواع الكروت',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // بطاقة إجمالي الكارتات الشاملة
                    _buildComprehensiveCardSummary(
                      sortedCardTypes,
                      cardTypeProvider,
                    ),

                    const SizedBox(height: 16),

                    // بطاقة نشاط العميل - عريضة
                    _buildWideActivityCard(
                      activityPercentage,
                      recentDebts.length,
                      customerDebts.length,
                    ),

                    const SizedBox(height: 16),

                    // عرض أنواع الكروت في عمود
                    Column(
                      children: sortedCardTypes.map((cardTypeEntry) {
                        final cardTypeId = cardTypeEntry.key;
                        final cardTypeData = cardTypeEntry.value;

                        final amount = cardTypeData['amount'] as double;
                        final quantity = cardTypeData['quantity'] as int;

                        // تحويل نوع الكارت إلى اسم عربي أولاً
                        final String cardTypeName = _getCardTypeDisplayName(
                          cardTypeId,
                          cardTypeProvider,
                        );

                        // تحديد اللون بناءً على نوع الكارت
                        MaterialColor color;
                        if (cardTypeName == 'زين') {
                          color = Colors.purple; // بنفسجي فاتح لزين
                        } else if (cardTypeName == 'أبو العشرة') {
                          color = Colors.cyan; // فايروسي فاتح لأبو العشرة
                        } else if (cardTypeName == 'آسيا') {
                          color = Colors.red;
                        } else if (cardTypeName == 'أبو الستة') {
                          color = Colors.grey;
                        } else {
                          // للأنواع الأخرى، استخدم الألوان الافتراضية
                          final colors = [
                            Colors.orange,
                            Colors.blue,
                            Colors.green,
                            Colors.teal,
                            Colors.indigo,
                          ];
                          final colorIndex =
                              sortedCardTypes.indexOf(cardTypeEntry);
                          color = colors[colorIndex % colors.length];
                        }

                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: _buildCardTypeStatCard(
                            cardTypeName,
                            quantity,
                            amount,
                            color,
                            customerDebts.length,
                            customerDebts.fold(
                                0, (sum, debt) => sum + debt.quantity),
                            cardTypeData['count'] as int,
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 20),
                  ],

                  // الصف الأول - مبيعات اليوم والأمس
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _navigateToCustomerTodayDebts(context),
                          child: _buildStatCard(
                            'مبيعات اليوم',
                            NumberFormatter.formatNumber(todayCount),
                            Icons.today,
                            Colors.blue,
                            'كارت',
                            subtitle: todayCardDetails,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: GestureDetector(
                          onTap: () =>
                              _navigateToCustomerYesterdayDebts(context),
                          child: _buildStatCard(
                            'مبيعات الأمس',
                            NumberFormatter.formatNumber(yesterdayCount),
                            Icons.history,
                            Colors.teal,
                            'كارت',
                            subtitle: yesterdayCardDetails,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // الصف الثاني - مبيعات الأسبوع والشهر
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'مبيعات الأسبوع',
                          _formatNumber(weekSales),
                          Icons.date_range,
                          Colors.green,
                          'د.ع',
                          subtitle: weekCardDetails,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'مبيعات الشهر',
                          _formatNumber(monthSales),
                          Icons.calendar_month,
                          Colors.purple,
                          'د.ع',
                          subtitle: monthCardDetails,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // الصف الرابع - الديون المتأخرة والمستحقة اليوم
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (overdueDebts.isNotEmpty) {
                              _showOverdueDebtsDialog(context, overdueDebts);
                            }
                          },
                          child: _buildStatCard(
                            'ديون متأخرة',
                            NumberFormatter.formatCurrency(
                                overdueAmount.toInt()),
                            Icons.warning,
                            Colors.red,
                            'د.ع',
                            subtitle: overdueCardDetails,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (dueTodayDebts.isNotEmpty) {
                              _showDueTodayDebtsDialog(context, dueTodayDebts);
                            }
                          },
                          child: _buildStatCard(
                            'مستحق اليوم',
                            NumberFormatter.formatCurrency(
                                dueTodayAmount.toInt()),
                            Icons.schedule,
                            Colors.orange,
                            'د.ع',
                            subtitle: dueTodayCardDetails,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // دوال التنقل للإحصائيات
  void _navigateToCustomerTodayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDailyDebtsScreen(
          customer: customer,
          title: 'ديون اليوم',
          isToday: true,
        ),
      ),
    );
  }

  void _navigateToCustomerYesterdayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDailyDebtsScreen(
          customer: customer,
          title: 'ديون الأمس',
          isToday: false,
        ),
      ),
    );
  }

  // تنسيق الأرقام
  String _formatNumber(double number) {
    return NumberFormatter.formatCurrency(number.toInt());
  }

  // بناء بطاقة إحصائية مع subtitle
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.cyan.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.shade100, color.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(
              icon,
              color: color.shade700,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'المبلغ: ',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
              Flexible(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: color.shade800,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.visible,
              maxLines: 3,
            ),
          ] else
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.access_time,
                  size: 10,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(width: 2),
                Text(
                  'محدث الآن',
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.grey.shade400,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  // بناء بطاقة إجمالي الكارتات الشاملة
  Widget _buildComprehensiveCardSummary(
    List<MapEntry<String, Map<String, dynamic>>> sortedCardTypes,
    CardTypeProvider cardTypeProvider,
  ) {
    // حساب الإجماليات بشكل صحيح مع معالجة جميع الحالات
    int totalQuantity = 0;
    double totalAmount = 0.0;

    for (final entry in sortedCardTypes) {
      final data = entry.value;

      // محاولة الحصول على الكمية من مفاتيح مختلفة
      final quantity = (data['quantity'] as int?) ??
          (data['total_quantity'] as int?) ??
          (data['count'] as int?) ??
          0;

      // محاولة الحصول على المبلغ من مفاتيح مختلفة
      final amount = (data['amount'] as double?) ??
          (data['total_amount'] as double?) ??
          (data['amount'] as int?)?.toDouble() ??
          0.0;

      totalQuantity += quantity;
      totalAmount += amount;
    }

    // التأكد من أن القيم ليست سالبة
    totalQuantity = totalQuantity < 0 ? 0 : totalQuantity;
    totalAmount = totalAmount < 0 ? 0.0 : totalAmount;

    // إذا لم توجد بيانات، عرض رسالة
    if (sortedCardTypes.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Center(
          child: Text(
            'لا توجد كارتات لعرضها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.blue.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان الرئيسي
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade500, Colors.blue.shade700],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.assessment,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'ملخص شامل للكارتات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1565C0),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // الإحصائيات الرئيسية
          Column(
            children: [
              // البطاقة الرئيسية: إجمالي الكارتات
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade50, Colors.blue.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.blue.shade200),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade500, Colors.blue.shade700],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.3),
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.inventory_2,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      totalQuantity > 0
                          ? NumberFormatter.formatNumber(totalQuantity)
                          : '0',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'إجمالي الكارتات',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // بطاقة إجمالي المبلغ
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade50, Colors.green.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.green.shade200),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.green.shade500,
                            Colors.green.shade700
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withValues(alpha: 0.3),
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.attach_money,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      totalAmount > 0
                          ? NumberFormatter.formatCurrency(totalAmount.toInt())
                          : '0 د.ع',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade800,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'إجمالي المبلغ',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // الصف الثالث: عدد الأنواع فقط
              // بطاقة عدد الأنواع
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade50, Colors.orange.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.orange.shade400,
                            Colors.orange.shade600
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.category,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${sortedCardTypes.length}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade800,
                          ),
                        ),
                        Text(
                          'أنواع مختلفة',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // تحويل نوع الكارت إلى اسم عربي
  String _getCardTypeDisplayName(
    String cardTypeId,
    CardTypeProvider cardTypeProvider,
  ) {
    // أولاً، تحقق من الكارتات الافتراضية
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardTypeId,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الكارتات الافتراضية، ابحث في المخصصة
      try {
        // البحث بالـ ID المباشر
        final cardType = cardTypeProvider.customCardTypes
            .firstWhere((type) => type.id.toString() == cardTypeId);
        return cardType.displayName;
      } catch (e) {
        // البحث بـ custom_ prefix
        try {
          final customId = cardTypeId.startsWith('custom_')
              ? cardTypeId.substring(7)
              : cardTypeId;
          final cardType = cardTypeProvider.customCardTypes
              .firstWhere((type) => type.id.toString() == customId);
          return cardType.displayName;
        } catch (e) {
          // إذا لم يوجد، تحويل يدوي للأسماء الشائعة
          return _convertCardTypeToArabic(cardTypeId);
        }
      }
    }
  }

  // تحويل أسماء الكارتات إلى العربية (fallback)
  String _convertCardTypeToArabic(String cardType) {
    final cleanType = cardType.trim().toLowerCase();

    switch (cleanType) {
      case 'zain':
      case 'زين':
        return 'زين';
      case 'sia':
      case 'asia':
      case 'آسيا':
      case 'اسيا':
        return 'آسيا';
      case 'abuashara':
      case 'أبو العشرة':
      case 'ابو العشرة':
        return 'أبو العشرة';
      case 'abusitta':
      case 'أبو الستة':
      case 'ابو الستة':
        return 'أبو الستة';
      case 'cash':
      case 'نقدي':
        return 'نقدي';
      case 'visa':
      case 'فيزا':
        return 'فيزا';
      case 'mastercard':
      case 'ماستركارد':
        return 'ماستركارد';
      case 'americanexpress':
      case 'أمريكان إكسبريس':
        return 'أمريكان إكسبريس';
      default:
        return cardType.isNotEmpty ? cardType : 'غير محدد';
    }
  }

  // بناء بطاقة إحصائيات نوع الكارت (نفس تصميم الإحصائيات المتكاملة)
  Widget _buildCardTypeStatCard(
    String cardTypeName,
    int quantity,
    double amount,
    MaterialColor color,
    int totalDebts,
    int totalQuantity,
    int cardCount,
  ) {
    final percentage =
        totalQuantity > 0 ? (quantity / totalQuantity * 100) : 0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: color.withValues(alpha: 0.1))],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          // Header مع اسم البطاقة والنسبة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.credit_card, color: color, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    cardTypeName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${percentage.toStringAsFixed(1)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // المحتوى مع الإحصائيات
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // العمليات (عدد الديون)
                Expanded(
                  child: _buildMiniStatItem(
                    'العمليات',
                    cardCount.toString(),
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade200),
                // الكمية
                Expanded(
                  child: _buildMiniStatItem(
                    'الكمية',
                    NumberFormatter.formatNumber(quantity),
                    Icons.inventory_2,
                    Colors.green,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade200),
                // المبلغ
                Expanded(
                  child: _buildMiniStatItem(
                    'المبلغ',
                    NumberFormatter.formatCurrency(amount.toInt()),
                    Icons.attach_money,
                    color,
                  ),
                ),
              ],
            ),
          ),

          // شريط التقدم
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // عنصر إحصائية صغير
  Widget _buildMiniStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  // بناء بطاقة نشاط العميل العريضة
  Widget _buildWideActivityCard(
    double activityPercentage,
    int recentDebtsCount,
    int totalDebtsCount,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.cyan.shade600, Colors.cyan.shade700],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.cyan.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.trending_up,
              size: 32,
              color: Colors.white,
            ),
          ),

          const SizedBox(width: 16),

          // المحتوى الرئيسي
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'نشاط العميل',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'آخر 30 يوم - $recentDebtsCount من $totalDebtsCount ديون',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),

          // النسبة والمؤشر
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${activityPercentage.toStringAsFixed(1)}%',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Container(
                width: 60,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: activityPercentage / 100,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// كلاس منفصل لمحتوى إحصائيات التسديدات
class _PaymentStatisticsContent extends StatelessWidget {
  const _PaymentStatisticsContent({required this.customer});
  final Customer customer;

  @override
  Widget build(BuildContext context) {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == customer.id)
            .toList();

        if (customerPayments.isEmpty) {
          return const Center(
            child: Text(
              'لا توجد تسديدات لهذا العميل',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          );
        }

        // حساب التواريخ
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final yesterday = today.subtract(const Duration(days: 1));
        final weekStart = today.subtract(Duration(days: today.weekday - 1));
        final monthStart = DateTime(now.year, now.month);

        // حساب تسديدات اليوم
        final todayPayments = customerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          return paymentDate.isAtSameMomentAs(today);
        }).toList();
        final todayAmount =
            todayPayments.fold(0.0, (sum, payment) => sum + payment.amount);

        // حساب تسديدات الأمس
        final yesterdayPayments = customerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          return paymentDate.isAtSameMomentAs(yesterday);
        }).toList();
        final yesterdayAmount =
            yesterdayPayments.fold(0.0, (sum, payment) => sum + payment.amount);

        // حساب تسديدات هذا الأسبوع
        final weekEnd = weekStart.add(const Duration(days: 6));
        final weekPayments = customerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          // التحقق من أن التاريخ ضمن الأسبوع الحالي
          return paymentDate
                  .isAfter(weekStart.subtract(const Duration(days: 1))) &&
              paymentDate.isBefore(weekEnd.add(const Duration(days: 1)));
        }).toList();
        final weekAmount =
            weekPayments.fold(0.0, (sum, payment) => sum + payment.amount);

        // حساب تسديدات هذا الشهر
        final monthEnd = DateTime(now.year, now.month + 1, 0);
        final monthPayments = customerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          // التحقق من أن التاريخ ضمن الشهر الحالي
          return paymentDate
                  .isAfter(monthStart.subtract(const Duration(days: 1))) &&
              paymentDate.isBefore(monthEnd.add(const Duration(days: 1)));
        }).toList();
        final monthAmount =
            monthPayments.fold(0.0, (sum, payment) => sum + payment.amount);

        // حساب إجمالي التسديدات
        final totalPayments = customerPayments.fold(
          0.0,
          (sum, payment) => sum + payment.amount,
        );

        return FutureBuilder<List<Debt>>(
          future: DatabaseHelper().getCustomerDebts(customer.id!),
          builder: (context, snapshot) {
            // حساب تفاصيل الكارتات حسب النوع
            final cardTypeStats = <String, Map<String, dynamic>>{};
            double totalCardsCount = 0;
            String cardDetailsText = '';

            if (snapshot.hasData) {
              final customerDebts = snapshot.data!;

              for (final debt in customerDebts) {
                if (debt.paidAmount > 0) {
                  final cardTypeName = debt.cardType;
                  final paidRatio = debt.paidAmount / debt.amount;
                  final paidQuantity = debt.quantity * paidRatio;
                  final paidAmount = debt.paidAmount;

                  if (cardTypeStats.containsKey(cardTypeName)) {
                    cardTypeStats[cardTypeName]!['quantity'] =
                        (cardTypeStats[cardTypeName]!['quantity'] as double) +
                            paidQuantity;
                    cardTypeStats[cardTypeName]!['amount'] =
                        (cardTypeStats[cardTypeName]!['amount'] as double) +
                            paidAmount;
                  } else {
                    cardTypeStats[cardTypeName] = {
                      'quantity': paidQuantity,
                      'amount': paidAmount,
                    };
                  }
                  totalCardsCount += paidQuantity;
                }
              }

              // تكوين نص التفاصيل
              if (cardTypeStats.isNotEmpty) {
                final details = cardTypeStats.entries.map((entry) {
                  final cardType = entry.key;
                  final quantity = (entry.value['quantity'] as double).round();
                  final amount = entry.value['amount'] as double;

                  // تحويل اسم الكارت للعربية
                  final displayName = _getCardTypeDisplayName(cardType);

                  return '$displayName: $quantity (${NumberFormatter.formatCurrency(amount.toInt())})';
                }).join(' • ');
                cardDetailsText = details;
              } else {
                cardDetailsText =
                    '${totalCardsCount.round()} كارت • ${customerPayments.length} تسديد';
              }
            } else {
              cardDetailsText = '${customerPayments.length} تسديد';
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات عامة للتسديدات
                  const Text(
                    'إحصائيات التسديدات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // بطاقات الإحصائيات
                  Column(
                    children: [
                      // بطاقة إجمالي التسديدات الشاملة
                      snapshot.hasData
                          ? _buildComprehensivePaymentSummary(
                              customerPayments,
                              snapshot.data!,
                            )
                          : Container(
                              padding: const EdgeInsets.all(20),
                              child: const Center(
                                child: Text('لا توجد بيانات'),
                              ),
                            ),

                      const SizedBox(height: 16),

                      // الصف الأول: تسديد اليوم والأمس
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => navigateToFilteredPayments(
                                context,
                                'تسديدات اليوم',
                                'today',
                              ),
                              child: buildStatCard(
                                'تسديد اليوم',
                                NumberFormatter.formatCurrency(
                                    todayAmount.toInt()),
                                Icons.today,
                                Colors.green,
                                'د.ع',
                                subtitle: snapshot.hasData
                                    ? _calculateCardDetails(
                                        todayPayments, snapshot.data!)
                                    : '${todayPayments.length} تسديد',
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => navigateToFilteredPayments(
                                context,
                                'تسديدات الأمس',
                                'yesterday',
                              ),
                              child: buildStatCard(
                                'تسديد الأمس',
                                NumberFormatter.formatCurrency(
                                    yesterdayAmount.toInt()),
                                Icons.history,
                                Colors.blue,
                                'د.ع',
                                subtitle: snapshot.hasData
                                    ? _calculateCardDetails(
                                        yesterdayPayments, snapshot.data!)
                                    : '${yesterdayPayments.length} تسديد',
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // الصف الثاني: تسديد هذا الأسبوع والشهر
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => navigateToFilteredPayments(
                                context,
                                'تسديدات هذا الأسبوع',
                                'week',
                              ),
                              child: buildStatCard(
                                'هذا الأسبوع',
                                NumberFormatter.formatCurrency(
                                    weekAmount.toInt()),
                                Icons.date_range,
                                Colors.purple,
                                'د.ع',
                                subtitle: snapshot.hasData
                                    ? _calculateCardDetails(
                                        weekPayments, snapshot.data!)
                                    : '${weekPayments.length} تسديد',
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => navigateToFilteredPayments(
                                context,
                                'تسديدات هذا الشهر',
                                'month',
                              ),
                              child: buildStatCard(
                                'هذا الشهر',
                                NumberFormatter.formatCurrency(
                                    monthAmount.toInt()),
                                Icons.calendar_month,
                                Colors.orange,
                                'د.ع',
                                subtitle: snapshot.hasData
                                    ? _calculateCardDetails(
                                        monthPayments, snapshot.data!)
                                    : '${monthPayments.length} تسديد',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // الانتقال إلى قائمة التسديدات المفلترة بناءً على نوع الفلترة
  void navigateToFilteredPayments(
    BuildContext context,
    String title,
    String filterType,
  ) {
    // الحصول على جميع تسديدات العميل
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final allCustomerPayments = debtProvider.payments
        .where((payment) => payment.customerId == customer.id)
        .toList();

    // فلترة التسديدات حسب النوع
    List<Payment> filteredPayments = [];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (filterType) {
      case 'today':
        filteredPayments = allCustomerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          return paymentDate.isAtSameMomentAs(today);
        }).toList();
        break;

      case 'yesterday':
        final yesterday = today.subtract(const Duration(days: 1));
        filteredPayments = allCustomerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          return paymentDate.isAtSameMomentAs(yesterday);
        }).toList();
        break;

      case 'week':
        // حساب من بداية الأسبوع (الاثنين) حتى ما قبل أمس (استثناء اليوم والأمس)
        final weekStart = today.subtract(
            Duration(days: today.weekday - 1)); // بداية الأسبوع (الاثنين)
        final yesterday = today.subtract(const Duration(days: 1));
        final dayBeforeYesterday =
            today.subtract(const Duration(days: 2)); // ما قبل أمس

        filteredPayments = allCustomerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          // التحقق من أن التاريخ من بداية الأسبوع حتى ما قبل أمس (استثناء اليوم والأمس)
          return !paymentDate.isBefore(weekStart) &&
              !paymentDate.isAfter(dayBeforeYesterday) &&
              !paymentDate.isAtSameMomentAs(today) &&
              !paymentDate.isAtSameMomentAs(yesterday);
        }).toList();
        break;

      case 'month':
        // فلترة التسديدات للشهر الحالي (استثناء اليوم والأمس وهذا الأسبوع)
        final weekStart = today.subtract(Duration(days: today.weekday - 1));
        final yesterday = today.subtract(const Duration(days: 1));

        filteredPayments = allCustomerPayments.where((payment) {
          final paymentDate = DateTime(
            payment.paymentDate.year,
            payment.paymentDate.month,
            payment.paymentDate.day,
          );
          // التحقق من أن التسديد في نفس الشهر والسنة الحالية
          // ولكن ليس اليوم أو الأمس أو ضمن الأسبوع الحالي
          return paymentDate.year == now.year &&
              paymentDate.month == now.month &&
              !paymentDate.isAtSameMomentAs(today) &&
              !paymentDate.isAtSameMomentAs(yesterday) &&
              paymentDate.isBefore(weekStart);
        }).toList();
        break;
    }

    // التحقق من وجود تسديدات
    if (filteredPayments.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('لا توجد تسديدات في $title'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // الانتقال إلى شاشة قائمة التسديدات المفلترة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerPaymentsScreen(
          customer: customer,
          filteredPayments: filteredPayments,
          filterTitle: title,
        ),
      ),
    );
  }

  // بطاقة إحصائية عريضة بدون مبلغ
  Widget buildWideStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // شريط جانبي ملون لتمييز حالة التسديد
          Container(
            width: 6,
            height: 100,
            decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
          ),

          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // الأيقونة
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // المحتوى
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          label,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              value,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: color,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              unit,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ],
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية مع أيقونة مخصصة
  Widget buildCustomStatCard(
    String title,
    String value,
    String iconAssetPath,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.cyan.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.shade100, color.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Image.asset(
              iconAssetPath,
              width: 24,
              height: 24,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.calendar_today,
                  size: 24,
                  color: color,
                );
              },
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  // بناء بطاقة إجمالي التسديدات الشاملة
  Widget _buildComprehensivePaymentSummary(
    List<Payment> customerPayments,
    List<Debt> customerDebts,
  ) {
    // حساب تفاصيل الكارتات للتسديدات
    final cardTypeStats = <String, Map<String, dynamic>>{};
    double totalAmount = 0;
    double totalCards = 0;

    for (final payment in customerPayments) {
      totalAmount += payment.amount;

      // البحث عن الدين المرتبط بالتسديد
      final debt =
          customerDebts.where((d) => d.id == payment.debtId).firstOrNull;

      if (debt != null) {
        final cardTypeName = debt.cardType;
        final paidRatio = payment.amount / debt.amount;
        final paidQuantity = debt.quantity * paidRatio;

        totalCards += paidQuantity;

        if (cardTypeStats.containsKey(cardTypeName)) {
          cardTypeStats[cardTypeName]!['quantity'] =
              (cardTypeStats[cardTypeName]!['quantity'] as double) +
                  paidQuantity;
          cardTypeStats[cardTypeName]!['amount'] =
              (cardTypeStats[cardTypeName]!['amount'] as double) +
                  payment.amount;
        } else {
          cardTypeStats[cardTypeName] = {
            'quantity': paidQuantity,
            'amount': payment.amount,
          };
        }
      }
    }

    // إذا لم توجد بيانات، عرض رسالة
    if (cardTypeStats.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Center(
          child: Text(
            'لا توجد تسديدات لعرضها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.green.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان الرئيسي
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade500, Colors.green.shade700],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.account_balance_wallet,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'ملخص شامل للتسديدات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // الإحصائيات الرئيسية
          Column(
            children: [
              // البطاقة الرئيسية: إجمالي المبلغ
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // إجمالي التسديدات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'إجمالي التسديدات:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          NumberFormatter.formatCurrency(totalAmount.toInt()),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // إجمالي الكارتات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'إجمالي الكارتات:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          '${totalCards.round()} كارت',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // بطاقات أنواع الكارتات
              ...cardTypeStats.entries.map((entry) {
                final cardType = entry.key;
                final quantity = (entry.value['quantity'] as double).round();
                final amount = entry.value['amount'] as double;

                // تحويل اسم الكارت للعربية
                final displayName = _getCardTypeDisplayName(cardType);

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.credit_card,
                        color: Colors.green.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '$displayName: $quantity كارت',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      Text(
                        NumberFormatter.formatCurrency(amount.toInt()),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية
  Widget buildStatCard(
    String title,
    String value,
    IconData icon,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.cyan.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.shade100, color.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(
              icon,
              color: color.shade700,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color.shade800,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 9,
                color: color.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          Text(
            unit,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إجمالي التسديدات العريضة
  Widget buildWideTotalPaymentCard(double totalPayments, int paymentCount) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade600, Colors.green.shade700],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.account_balance_wallet,
              size: 32,
              color: Colors.white,
            ),
          ),

          const SizedBox(width: 16),

          // المحتوى الرئيسي
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي التسديدات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'جميع التسديدات المسجلة للعميل',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),

          // المبلغ وعدد التسديدات
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                NumberFormatter.formatCurrency(totalPayments.toInt()),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                '$paymentCount تسديد',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // حساب تفاصيل الكارتات لمجموعة من التسديدات
  String _calculateCardDetails(
      List<Payment> payments, List<Debt> customerDebts) {
    final cardTypeStats = <String, Map<String, dynamic>>{};

    for (final payment in payments) {
      // البحث عن الدين المرتبط بالتسديد
      final debt =
          customerDebts.where((d) => d.id == payment.debtId).firstOrNull;

      if (debt != null) {
        final cardTypeName = debt.cardType;
        final paidRatio = payment.amount / debt.amount;
        final paidQuantity = debt.quantity * paidRatio;

        if (cardTypeStats.containsKey(cardTypeName)) {
          cardTypeStats[cardTypeName]!['quantity'] =
              (cardTypeStats[cardTypeName]!['quantity'] as double) +
                  paidQuantity;
          cardTypeStats[cardTypeName]!['amount'] =
              (cardTypeStats[cardTypeName]!['amount'] as double) +
                  payment.amount;
        } else {
          cardTypeStats[cardTypeName] = {
            'quantity': paidQuantity,
            'amount': payment.amount,
          };
        }
      }
    }

    if (cardTypeStats.isEmpty) {
      return '${payments.length} تسديد';
    }

    final details = cardTypeStats.entries.map((entry) {
      final cardType = entry.key;
      final quantity = (entry.value['quantity'] as double).round();

      // تحويل اسم الكارت للعربية
      final String displayName = _getCardTypeDisplayName(cardType);

      return '$displayName: $quantity';
    }).join(' • ');

    return details;
  }

  // تحويل اسم الكارت للعربية
  String _getCardTypeDisplayName(String cardType) {
    final cleanType = cardType.trim().toLowerCase();

    // التعامل مع البطاقات المخصصة
    if (cleanType.startsWith('custom_')) {
      try {
        final customNumber = cleanType.replaceAll('custom_', '');
        switch (customNumber) {
          case '1':
          case '3':
            return 'أبو الستة'; // فئة 6000
          case '2':
          case '4':
            return 'آسيا'; // فئة 5000
          case '5':
          case '6':
            return 'زين'; // فئة 5000
          case '7':
          case '8':
            return 'أبو العشرة'; // فئة 10000
          default:
            return 'بطاقة مخصصة $customNumber';
        }
      } catch (e) {
        return cardType.isNotEmpty ? cardType : 'غير محدد';
      }
    }

    // إذا كان النص عربي بالفعل، أعده كما هو
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(cardType)) {
      return cardType;
    }

    // التعامل مع الأنواع الافتراضية
    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia':
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      default:
        return cardType.isNotEmpty ? cardType : 'غير محدد';
    }
  }
}
