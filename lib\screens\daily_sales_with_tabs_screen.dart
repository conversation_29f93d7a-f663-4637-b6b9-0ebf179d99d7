import 'package:flutter/material.dart';
import 'daily_sales_debts_screen.dart';

class DailySalesWithTabsScreen extends StatefulWidget {
  const DailySalesWithTabsScreen({super.key});

  @override
  State<DailySalesWithTabsScreen> createState() =>
      _DailySalesWithTabsScreenState();
}

class _DailySalesWithTabsScreenState extends State<DailySalesWithTabsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'المبيعات اليومية',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.teal.shade600,
                Colors.teal.shade700,
                Colors.cyan.shade800,
              ],
            ),
          ),
        ),
        toolbarHeight: 50, // تقليل ارتفاع الشريط العلوي
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(40), // تقليل ارتفاع التبويبات
          child: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
            tabs: const [
              Tab(
                text: 'بيع اليوم',
                height: 40,
              ),
              Tab(
                text: 'بيع أمس',
                height: 40,
              ),
            ],
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          // تبويب بيع اليوم
          DailySalesDebtsScreen(
            isToday: true,
            title: 'بيع اليوم',
          ),
          // تبويب بيع أمس
          DailySalesDebtsScreen(
            isToday: false,
            title: 'بيع أمس',
          ),
        ],
      ),
    );
  }
}
