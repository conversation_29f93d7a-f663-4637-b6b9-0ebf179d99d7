import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../models/payment.dart';
import '../providers/debt_provider.dart';
import '../widgets/payments_tab.dart';

class CustomerPaymentsScreen extends StatefulWidget {
  const CustomerPaymentsScreen({
    super.key,
    required this.customer,
    this.filteredPayments,
    this.filterTitle,
  });
  final Customer customer;
  final List<Payment>? filteredPayments;
  final String? filterTitle;

  @override
  State<CustomerPaymentsScreen> createState() => _CustomerPaymentsScreenState();
}

class _CustomerPaymentsScreenState extends State<CustomerPaymentsScreen> {
  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};
  PaymentCardViewType _currentViewType = PaymentCardViewType.standard;

  @override
  void initState() {
    super.initState();
    _loadViewType();
    // Load customer payments
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerPayments(widget.customer.id!);
    });
  }

  // تحميل نوع العرض المحفوظ
  Future<void> _loadViewType() async {
    try {
      // إجبار استخدام العرض المصغر فقط
      setState(() {
        _currentViewType = PaymentCardViewType.mini;
      });
      debugPrint('تم إجبار نوع العرض على المصغر: ${_currentViewType.name}');
    } catch (e) {
      debugPrint('خطأ في تحميل نوع عرض التسديدات: $e');
      setState(() {
        _currentViewType = PaymentCardViewType.mini;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
        'CustomerPaymentsScreen build - نوع العرض الحالي: ${_currentViewType.name}');

    return Scaffold(
      appBar: _isSelectionMode ? _buildSelectionAppBar() : _buildNormalAppBar(),
      body: PaymentsTab(
        key: ValueKey('payments_tab_${_currentViewType.name}'),
        customer: widget.customer,
        isSelectionMode: _isSelectionMode,
        selectedPaymentIds: _selectedPaymentIds,
        currentViewType: _currentViewType,
        filteredPayments: widget.filteredPayments,
        onSelectionChanged: (paymentId, isSelected) {
          setState(() {
            if (isSelected) {
              _selectedPaymentIds.add(paymentId);
            } else {
              _selectedPaymentIds.remove(paymentId);
            }
          });
        },
      ),
    );
  }

  AppBar _buildNormalAppBar() {
    final title = widget.filterTitle != null
        ? '${widget.filterTitle} - ${widget.customer.name}'
        : 'تسديدات ${widget.customer.name}';

    return AppBar(
      title: Text(title),
      backgroundColor: const Color(0xFF00695C),
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    );
  }

  AppBar _buildSelectionAppBar() {
    return AppBar(
      title: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: Row(
          key: ValueKey(_selectedPaymentIds.length),
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 18,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    '${_selectedPaymentIds.length}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Text(
              _selectedPaymentIds.length == 1 ? 'تسديد محدد' : 'تسديد محدد',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
      backgroundColor: const Color(0xFF1976D2),
      foregroundColor: Colors.white,
      elevation: 4,
      shadowColor: Colors.blue.withValues(alpha: 0.3),
      centerTitle: true,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          icon: const Icon(Icons.close, size: 20),
          onPressed: _toggleSelectionMode,
          tooltip: 'إلغاء التحديد',
          style: IconButton.styleFrom(padding: EdgeInsets.zero),
        ),
      ),
      actions: [
        // زر تحديد الكل
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: IconButton(
            icon: const Icon(Icons.select_all, size: 20),
            onPressed: _selectAllPayments,
            tooltip: 'تحديد الكل',
            style: IconButton.styleFrom(padding: const EdgeInsets.all(8)),
          ),
        ),
        // زر الحذف
        if (_selectedPaymentIds.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red[600],
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.delete_forever, size: 20),
              onPressed: _deleteSelectedPayments,
              tooltip: 'حذف المحدد',
              style: IconButton.styleFrom(
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(8),
              ),
            ),
          ),
        const SizedBox(width: 8),
      ],
    );
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedPaymentIds.clear();
      }
    });

    // إظهار رسالة توضيحية
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isSelectionMode
              ? 'تم تفعيل وضع التحديد المتعدد. اضغط على التسديدات لتحديدها.'
              : 'تم إلغاء وضع التحديد المتعدد.',
        ),
        backgroundColor: _isSelectionMode ? Colors.blue : Colors.grey,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _selectAllPayments() {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    setState(() {
      _selectedPaymentIds.clear();
      for (final payment in customerPayments) {
        if (payment.id != null) {
          _selectedPaymentIds.add(payment.id!);
        }
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديد ${_selectedPaymentIds.length} تسديد'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _deleteSelectedPayments() {
    if (_selectedPaymentIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.delete, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'حذف التسديدات المحددة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف ${_selectedPaymentIds.length} تسديد محدد نهائياً؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final debtProvider = Provider.of<DebtProvider>(
                  context,
                  listen: false,
                );

                final selectedCount = _selectedPaymentIds.length;
                for (final paymentId in _selectedPaymentIds) {
                  await debtProvider.deletePayment(paymentId);
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف $selectedCount تسديد نهائياً'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائياً',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
