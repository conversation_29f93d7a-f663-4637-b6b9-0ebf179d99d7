import 'package:intl/intl.dart';

class NumberFormatter {
  static final NumberFormat _currencyFormatter = NumberFormat('#,##0', 'en');
  static final NumberFormat _numberFormatter = NumberFormat('#,##0', 'en');

  /// تنسيق الأرقام مع فواصل الآلاف
  static String formatNumber(num number) {
    return _numberFormatter.format(number);
  }

  /// تنسيق العملة مع فواصل الآلاف ورمز العملة (بدون منازل عشرية)
  static String formatCurrency(num amount) {
    return '${_currencyFormatter.format(amount.round())} د.ع';
  }

  /// تنسيق العملة بدون رمز العملة (بدون منازل عشرية)
  static String formatCurrencyWithoutSymbol(num amount) {
    return _currencyFormatter.format(amount.round());
  }

  /// تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }
}
